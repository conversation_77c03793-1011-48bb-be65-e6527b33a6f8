plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'dev.flutter.flutter-gradle-plugin'
    id 'com.google.gms.google-services'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystorePropertiesFile.withReader('UTF-8') { reader ->
        keystoreProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.maximummdeia.oil_change_tracker"
    compileSdk = 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    // Java compiler options
    tasks.withType(JavaCompile) {
        options.compilerArgs << '-Xlint:-options'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    aaptOptions {
        cruncherEnabled = false
    }

    defaultConfig {
        applicationId "com.maximummdeia.oil_change_tracker"
        minSdk 24
        targetSdk = 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        
        // Add 16KB alignment for native libraries (Android 15+ requirement)
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }

        // Set this to true to support 16KB page alignment
        manifestPlaceholders = [
            extractNativeLibs: "true"
        ]
    }

    // Add proper 16KB page size alignment for libraries
    packagingOptions {
        jniLibs {
            useLegacyPackaging = false
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (keystorePropertiesFile.exists()) {
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
            } else {
                signingConfig signingConfigs.debug
                println "\n*** WARNING: key.properties not found. Using debug signing for release build. DO NOT PUBLISH THIS BUILD. ***\n"
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Disable font tree-shaking to ensure all font assets are preserved in the release build
            buildConfigField "boolean", "TREE_SHAKE_ICONS", "false"

            // Add explicit include for Material Icon font files
            resValue "string", "app_name", "Oil Plus"

            // Enable split APKs to reduce installation size
            splits {
                abi {
                    enable true
                    reset()
                    include 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
                    universalApk false
                }
            }

            // Include debug symbols
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }

            // Ensure consistent resources
            manifestPlaceholders = [
                appName: "Oil Plus",
                appIcon: "@mipmap/ic_launcher",
                appRoundIcon: "@mipmap/ic_launcher_round"
            ]
        }
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "app_name", "Oil Plus (Debug)"

            // Don't use a suffix to avoid Firebase configuration issues
            // applicationIdSuffix ".debug"

            // Disable splits for debug builds to fix Flutter build issues
            splits {
                abi {
                    enable false
                    universalApk true
                }
            }

            // Ensure consistent resources
            manifestPlaceholders = [
                appName: "Oil Plus (Debug)",
                appIcon: "@mipmap/ic_launcher",
                appRoundIcon: "@mipmap/ic_launcher_round"
            ]
        }
    }
}

flutter {
    source '../..'
}

// Add configuration to handle dependency resolution issues
configurations.all {
    // Globally exclude the old SafetyNet module
    exclude group: 'com.google.android.gms', module: 'play-services-safetynet'

    resolutionStrategy {
        // Use a consistent version for phenotype that's compatible with your device
        force 'com.google.android.gms:play-services-phenotype:17.0.0'
        force 'com.google.android.gms:play-services-basement:18.3.0'
        force 'com.google.android.gms:play-services-base:18.3.0'
        force 'com.google.android.gms:play-services-tasks:18.1.0'
        
        // Force compatible versions for ads components
        force 'com.google.android.gms:play-services-ads:22.6.0'
        force 'com.google.android.gms:play-services-ads-identifier:18.1.0'
        force 'com.google.android.gms:play-services-ads-lite:22.6.0'
        force 'com.google.android.gms:play-services-ads-base:22.6.0'
        
        // Remove the problematic eachDependency block that's causing issues
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation platform('com.google.firebase:firebase-bom:32.7.2')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    implementation 'com.google.android.gms:play-services-base:18.3.0'
    
    // Replace SafetyNet with Play Integrity
    implementation 'com.google.android.play:integrity:1.3.0'
    
    // Firebase App Check - SafetyNet should be excluded by the global rule now
    implementation 'com.google.firebase:firebase-appcheck-playintegrity:17.1.1'
    implementation 'com.google.firebase:firebase-appcheck-debug:17.1.1'
    
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.window:window:1.2.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.0'

    // Fix Google Play Services version inconsistencies - use consistent versions
    implementation 'com.google.android.gms:play-services-ads:22.6.0'
    implementation 'com.google.android.gms:play-services-tasks:18.1.0'
    implementation 'com.google.android.gms:play-services-basement:18.3.0'
    
    // Add explicit dependencies for ads-identifier and related components
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0'
    implementation 'com.google.android.gms:play-services-ads-base:22.6.0'
    implementation 'com.google.android.gms:play-services-ads-lite:22.6.0'
    implementation 'com.google.android.gms:play-services-measurement-sdk-api:21.5.1'
    implementation 'com.google.android.gms:play-services-measurement-base:21.5.1'
    
    // Add explicit dependency for ads-identifier to fix security exception
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0'
    
    // Use newer phenotype version
    implementation 'com.google.android.gms:play-services-phenotype:17.0.0'
    implementation 'com.google.android.ump:user-messaging-platform:2.1.0'

    // Add explicit compatible version of bouncycastle
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'

    // Add constraints block to strictly enforce the desired BouncyCastle version
    constraints {
      implementation('org.bouncycastle:bcprov-jdk15on:1.70') {
          because 'Ensures compatibility with Java 17 build environment'
      }
      implementation('org.bouncycastle:bcpkix-jdk15on:1.70') {
          because 'Ensures compatibility with Java 17 build environment'
      }
    }
}