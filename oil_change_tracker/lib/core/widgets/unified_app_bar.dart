import 'package:flutter/material.dart';
import '../theme/theme_extensions.dart';

/// A unified app bar component that provides consistent styling across all screens
class UnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool automaticallyImplyLeading;

  const UnifiedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = false,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          color: foregroundColor ?? context.accentColor,
          fontWeight: FontWeight.bold,
          fontSize: 22,
        ),
      ),
      backgroundColor: backgroundColor ?? context.containerBackgroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ?? (showBackButton && Navigator.canPop(context)
          ? IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: foregroundColor ?? context.accentColor,
              ),
              onPressed: onBackPressed ?? () => Navigator.pop(context),
            )
          : null),
      actions: actions?.map((action) {
        // If the action is an IconButton, ensure it uses the correct color
        if (action is IconButton) {
          return IconButton(
            icon: action.icon,
            onPressed: action.onPressed,
            tooltip: action.tooltip,
            color: foregroundColor ?? context.accentColor,
          );
        }
        return action;
      }).toList(),
      iconTheme: IconThemeData(
        color: foregroundColor ?? context.accentColor,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// A factory class for creating common app bar configurations
class AppBarFactory {
  /// Creates a standard app bar for most screens
  static UnifiedAppBar standard({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = false,
    VoidCallback? onBackPressed,
  }) {
    return UnifiedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      onBackPressed: onBackPressed,
    );
  }

  /// Creates an app bar for the dashboard screen
  static UnifiedAppBar dashboard({
    required String title,
    List<Widget>? actions,
  }) {
    return UnifiedAppBar(
      title: title,
      actions: actions,
      showBackButton: false,
      automaticallyImplyLeading: false,
    );
  }

  /// Creates an app bar for modal screens (like add/edit forms)
  static UnifiedAppBar modal({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
  }) {
    return UnifiedAppBar(
      title: title,
      actions: actions,
      centerTitle: true,
      onBackPressed: onBackPressed,
    );
  }

  /// Creates an app bar with custom colors
  static UnifiedAppBar custom({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = false,
    VoidCallback? onBackPressed,
    Color? backgroundColor,
    Color? foregroundColor,
    double elevation = 0,
  }) {
    return UnifiedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      onBackPressed: onBackPressed,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
    );
  }
}
