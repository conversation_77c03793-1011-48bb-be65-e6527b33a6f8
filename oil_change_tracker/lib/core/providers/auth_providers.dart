import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import 'dart:developer' as dev;

/// Provider for the AuthService instance
final authServiceProvider = Provider<AuthService>((ref) {
  final service = AuthService(container: ref.container);
  ref.onDispose(() => service.dispose());
  return service;
});

/// Provider for authentication state
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthStateNotifier(authService);
});

/// Provider for authentication process
final authProcessProvider = StateNotifierProvider<AuthProcessNotifier, AuthProcess>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthProcessNotifier(authService);
});

/// State notifier for auth state
class AuthStateNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  
  AuthStateNotifier(this._authService) : super(_authService.currentState) {
    // Listen for auth state changes from Firebase
    FirebaseAuth.instance.authStateChanges().listen((_) {
      // Update our state with the current state from the AuthService
      state = _authService.currentState;
      dev.log('AuthStateNotifier: State updated to $state');
    });
  }
}

/// State notifier for auth process
class AuthProcessNotifier extends StateNotifier<AuthProcess> {
  final AuthService _authService;
  
  AuthProcessNotifier(this._authService) : super(_authService.currentProcess) {
    // We don't have a direct stream for auth process, 
    // so we rely on periodic checks or updates through the auth functions
  }
  
  // Update state after an auth operation
  void updateProcess() {
    state = _authService.currentProcess;
    dev.log('AuthProcessNotifier: Process updated to $state');
  }
}

/// Provider for current Firebase user
final currentUserProvider = Provider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.currentUser;
});

/// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState == AuthState.authenticated;
});

/// Provider that indicates whether an auth process is in progress
final isAuthInProgressProvider = Provider<bool>((ref) {
  final authProcess = ref.watch(authProcessProvider);
  return authProcess != AuthProcess.idle;
}); 