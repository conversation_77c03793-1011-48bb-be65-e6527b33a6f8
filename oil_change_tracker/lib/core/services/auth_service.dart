import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/initialization_service.dart'; // Add import for appOpenAdManagerProvider

/// Enum representing possible authentication states
enum AuthState {
  initial,
  authenticated,
  unauthenticated,
  error
}

/// Enum representing active authentication processes
enum AuthProcess {
  idle,
  emailSignIn,
  emailSignUp,
  googleSignIn,
  signOut,
  passwordReset,
  profileUpdate,
  emailVerification
}

/// Class representing the result of an authentication operation
class AuthResult {
  final bool success;
  final User? user;
  final String? errorMessage;
  final dynamic originalError;

  AuthResult({
    required this.success,
    this.user,
    this.errorMessage,
    this.originalError,
  });

  factory AuthResult.success(User user) {
    return AuthResult(success: true, user: user);
  }

  factory AuthResult.failure(String message, [dynamic error]) {
    return AuthResult(
      success: false,
      errorMessage: message,
      originalError: error,
    );
  }
}

/// Single source of truth for authentication operations and state
class AuthService {
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;
  final FirebaseFirestore _firestore;
  final ProviderContainer? _container;

  // Current auth state
  AuthState _currentState = AuthState.initial;
  AuthState get currentState => _currentState;

  // Current auth process
  AuthProcess _currentProcess = AuthProcess.idle;
  AuthProcess get currentProcess => _currentProcess;

  // Constructor
  AuthService({
    FirebaseAuth? auth,
    GoogleSignIn? googleSignIn,
    FirebaseFirestore? firestore,
    ProviderContainer? container,
  }) :
    _auth = auth ?? FirebaseAuth.instance,
    _googleSignIn = googleSignIn ?? GoogleSignIn(
      // The following is required to make Google Sign-In work in release mode
      scopes: ['email', 'profile'],
      clientId: '952433993522-lpe7gg47nhrl18sh6psusliuihaejf8h.apps.googleusercontent.com', // Web client ID from google-services.json
    ),
    _firestore = firestore ?? FirebaseFirestore.instance,
    _container = container {
    // Initialize state based on current user
    _initialize();
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Initialize the service
  void _initialize() {
    // Update state based on current user
    final user = _auth.currentUser;
    _currentState = user != null ? AuthState.authenticated : AuthState.unauthenticated;

    // Listen for auth state changes
    _auth.authStateChanges().listen((User? user) {
      dev.log('AuthService: Auth state changed, user: ${user?.uid ?? 'null'}');
      _currentState = user != null ? AuthState.authenticated : AuthState.unauthenticated;
    });
  }

  // Update auth state
  void _updateAuthState(AuthState newState) {
    if (_currentState != newState) {
      dev.log('AuthService: Auth state changed from $_currentState to $newState');
      _currentState = newState;
    }
  }

  // Update auth process
  void _updateAuthProcess(AuthProcess newProcess) {
    if (_currentProcess != newProcess) {
      dev.log('AuthService: Auth process changed from $_currentProcess to $newProcess');
      _currentProcess = newProcess;
    }
  }

  // Sign in with email and password
  Future<AuthResult> signInWithEmail(String email, String password) async {
    try {
      _updateAuthProcess(AuthProcess.emailSignIn);

      dev.log('AuthService: Attempting email sign in for $email');

      // Attempt sign in
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return AuthResult.failure('No user returned from sign-in');
      }

      // Check if user document exists in Firestore, create if it doesn't
      try {
        final userDoc = await _firestore.collection('users').doc(credential.user!.uid).get();

        if (!userDoc.exists) {
          dev.log('AuthService: User document not found in Firestore, creating one');

          // Create user document
          final userData = {
            'id': credential.user!.uid,
            'email': credential.user!.email,
            'displayName': credential.user!.displayName ?? credential.user!.email!.split('@')[0],
            'photoUrl': credential.user!.photoURL,
            'isEmailVerified': credential.user!.emailVerified,
            'createdAt': FieldValue.serverTimestamp(),
            'lastSignIn': FieldValue.serverTimestamp(),
            'providers': {'email': true},
          };

          await _firestore.collection('users').doc(credential.user!.uid).set(
            userData,
            SetOptions(merge: true),
          );

          dev.log('AuthService: Created user document in Firestore for ${credential.user!.uid}');
        } else {
          // Update last sign-in timestamp and email verification status
          await _firestore.collection('users').doc(credential.user!.uid).update({
            'lastSignIn': FieldValue.serverTimestamp(),
            'isEmailVerified': credential.user!.emailVerified,
          });

          dev.log('AuthService: Updated last sign-in timestamp for ${credential.user!.uid}');
        }
      } catch (e) {
        dev.log('AuthService: Error checking/creating user document: $e');
        // Continue despite error - the user is still signed in
      }

      // If email is not verified, send verification email
      if (!credential.user!.emailVerified) {
        dev.log('AuthService: User email is not verified: ${credential.user!.email}');
        try {
          await credential.user!.sendEmailVerification();
          dev.log('AuthService: Sent verification email to ${credential.user!.email}');
        } catch (e) {
          dev.log('AuthService: Error sending verification email: $e');
          // Continue despite error - the user is still signed in
        }
      }

      dev.log('AuthService: Email sign in successful for ${credential.user!.uid}');
      return AuthResult.success(credential.user!);
    } on FirebaseAuthException catch (e) {
      dev.log('AuthService: Firebase Auth Exception during email sign-in: ${e.code} - ${e.message}');
      return AuthResult.failure(_getReadableErrorMessage(e), e);
    } catch (e) {
      dev.log('AuthService: Unexpected error during email sign-in: $e');
      return AuthResult.failure('An unexpected error occurred', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Sign up with email and password
  Future<AuthResult> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _updateAuthProcess(AuthProcess.emailSignUp);

      dev.log('AuthService: Attempting email sign up for $email');

      // Check if email already exists
      try {
        final methods = await _auth.fetchSignInMethodsForEmail(email);
        if (methods.isNotEmpty) {
          dev.log('AuthService: Email already exists with methods: $methods');
          return AuthResult.failure('This email is already in use. Please use a different email or try signing in.');
        }
      } catch (e) {
        dev.log('AuthService: Error checking existing email: $e');
        // Continue with sign-up attempt
      }

      // Attempt sign up
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return AuthResult.failure('No user returned from sign-up');
      }

      // Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        try {
          await credential.user!.updateDisplayName(displayName);
          dev.log('AuthService: Updated display name for new user');
        } catch (e) {
          dev.log('AuthService: Error updating display name: $e');
          // Continue despite error - we'll try to save in Firestore anyway
        }
      }

      // Always create user document in Firestore, regardless of display name
      try {
        final userData = {
          'id': credential.user!.uid,
          'email': email,
          'displayName': displayName ?? credential.user!.displayName ?? email.split('@')[0],
          'photoUrl': credential.user!.photoURL,
          'isEmailVerified': credential.user!.emailVerified,
          'createdAt': FieldValue.serverTimestamp(),
          'lastSignIn': FieldValue.serverTimestamp(),
          'providers': {'email': true},
        };

        await _firestore.collection('users').doc(credential.user!.uid).set(
          userData,
          SetOptions(merge: true),
        );

        dev.log('AuthService: Created user document in Firestore for ${credential.user!.uid}');
      } catch (e) {
        dev.log('AuthService: Error creating user document: $e');
        // Continue despite error - the user is still created
      }

      // Send email verification
      try {
        await credential.user!.sendEmailVerification();
        dev.log('AuthService: Sent email verification to ${credential.user!.email}');
      } catch (e) {
        dev.log('AuthService: Error sending verification email: $e');
        // Continue despite error - the user is still created
      }

      dev.log('AuthService: Email sign up successful for ${credential.user!.uid}');
      return AuthResult.success(credential.user!);
    } on FirebaseAuthException catch (e) {
      dev.log('AuthService: Firebase Auth Exception during email sign-up: ${e.code} - ${e.message}');
      return AuthResult.failure(_getReadableErrorMessage(e), e);
    } catch (e) {
      dev.log('AuthService: Unexpected error during email sign-up: $e');
      return AuthResult.failure('An unexpected error occurred', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Sign in with Google
  Future<AuthResult> signInWithGoogle() async {
    try {
      _updateAuthProcess(AuthProcess.googleSignIn);

      dev.log('AuthService: Attempting Google sign in');

      // Clear any existing sign-in state to avoid issues
      try {
        await _googleSignIn.signOut();
        // Only sign out of Google, not Firebase auth to avoid extra operations
        dev.log('AuthService: Cleared previous Google sign-in state');
      } catch (e) {
        dev.log('AuthService: Error during clearing previous state: $e');
        // We can continue even if this fails
      }

      // Remove delay that was unnecessarily slowing things down

      // Start Google sign-in flow
      dev.log('AuthService: Starting Google sign-in flow...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        dev.log('AuthService: Google sign-in was cancelled by user');
        _updateAuthProcess(AuthProcess.idle); // Reset auth process early to avoid UI lock
        return AuthResult.failure('Google Sign-In was cancelled');
      }

      dev.log('AuthService: Google account selected: ${googleUser.email}');

      // Get Google authentication details
      dev.log('AuthService: Getting Google auth details...');
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      dev.log('AuthService: Got Google auth tokens');

      // Create Firebase credential
      dev.log('AuthService: Creating Firebase credential...');
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in with Firebase
      try {
        dev.log('AuthService: Signing in with Firebase...');
        final userCredential = await _auth.signInWithCredential(credential);

        if (userCredential.user == null) {
          dev.log('AuthService: Firebase sign-in failed - no user returned');
          return AuthResult.failure('Failed to sign in with Firebase');
        }

        dev.log('AuthService: Firebase sign-in successful - user ID: ${userCredential.user!.uid}');

        // Update user in Firestore with simplified error handling
        // Move Firestore operations to background using Future.microtask to avoid delaying the login
        Future.microtask(() async {
          try {
            final userData = {
              'email': userCredential.user!.email,
              'displayName': userCredential.user!.displayName,
              'photoUrl': userCredential.user!.photoURL,
              'lastSignIn': FieldValue.serverTimestamp(),
              'createdAt': FieldValue.serverTimestamp(),
              'uid': userCredential.user!.uid,
            };

            // Use set with merge to update or create without complex validation
            await _firestore.collection('users').doc(userCredential.user!.uid).set(
              userData,
              SetOptions(merge: true),
            ).timeout(const Duration(seconds: 5));

            dev.log('AuthService: User updated in Firestore successfully');
          } catch (firestoreError) {
            dev.log('AuthService: Error updating user in Firestore (background): $firestoreError');
            // Non-blocking, user will still be signed in
          }
        });

        dev.log('AuthService: Google sign-in successful for ${userCredential.user!.uid}');
        return AuthResult.success(userCredential.user!);
      } catch (e) {
        // Check for PigeonUserDetails error
        if (e.toString().contains('PigeonUserDetails')) {
          dev.log('AuthService: PigeonUserDetails error detected, checking auth state');

          // Check current user immediately without delay
          final currentUser = _auth.currentUser;
          if (currentUser != null) {
            dev.log('AuthService: User is actually signed in (${currentUser.uid}) despite error');

            // Move Firestore operations to background
            Future.microtask(() async {
              try {
                await _firestore.collection('users').doc(currentUser.uid).set({
                  'email': currentUser.email,
                  'displayName': currentUser.displayName,
                  'photoUrl': currentUser.photoURL,
                  'lastSignIn': FieldValue.serverTimestamp(),
                  'createdAt': FieldValue.serverTimestamp(),
                  'uid': currentUser.uid,
                }, SetOptions(merge: true));
                dev.log('AuthService: User document created on background attempt');
              } catch (retryError) {
                dev.log('AuthService: Failed to create user document in background: $retryError');
              }
            });

            return AuthResult.success(currentUser);
          }
        }

        dev.log('AuthService: Error during Firebase sign-in: $e');
        return AuthResult.failure('Failed to sign in with Google', e);
      }
    } catch (e) {
      dev.log('AuthService: Unexpected error during Google sign-in: $e');
      return AuthResult.failure('An unexpected error occurred', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Sign out
  Future<AuthResult> signOut() async {
    try {
      _updateAuthProcess(AuthProcess.signOut);

      dev.log('AuthService: Attempting sign out');

      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);

      // Disable app open ads after sign out
      if (_container != null) {
        final adManager = _container.read(appOpenAdManagerProvider);
        adManager.showAdsAfterLogin = false;
      }

      dev.log('AuthService: Sign out successful');
      _updateAuthProcess(AuthProcess.idle);
      return AuthResult(success: true);
    } catch (e) {
      dev.log('AuthService: Error during sign out: $e');
      _updateAuthProcess(AuthProcess.idle);
      return AuthResult(
        success: false,
        errorMessage: 'Failed to sign out: ${e.toString()}',
      );
    }
  }

  // Reset password
  Future<AuthResult> resetPassword(String email) async {
    try {
      _updateAuthProcess(AuthProcess.passwordReset);

      dev.log('AuthService: Attempting password reset for $email');

      await _auth.sendPasswordResetEmail(email: email);

      dev.log('AuthService: Password reset email sent to $email');
      return AuthResult(success: true);
    } on FirebaseAuthException catch (e) {
      dev.log('AuthService: Firebase Auth Exception during password reset: ${e.code} - ${e.message}');
      return AuthResult.failure(_getReadableErrorMessage(e), e);
    } catch (e) {
      dev.log('AuthService: Unexpected error during password reset: $e');
      return AuthResult.failure('An unexpected error occurred', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Send email verification
  Future<AuthResult> sendEmailVerification() async {
    try {
      _updateAuthProcess(AuthProcess.emailVerification);

      final user = _auth.currentUser;
      if (user == null) {
        dev.log('AuthService: No user logged in to send verification email');
        return AuthResult.failure('No user logged in');
      }

      dev.log('AuthService: Attempting to send verification email to ${user.email}');

      // Force token refresh to ensure we have the latest auth state
      await user.getIdToken(true);

      // Send verification email
      await user.sendEmailVerification();

      dev.log('AuthService: Verification email sent to ${user.email}');
      return AuthResult(success: true);
    } on FirebaseAuthException catch (e) {
      dev.log('AuthService: Firebase Auth Exception during email verification: ${e.code} - ${e.message}');
      return AuthResult.failure(_getReadableErrorMessage(e), e);
    } catch (e) {
      dev.log('AuthService: Unexpected error during email verification: $e');
      return AuthResult.failure('An unexpected error occurred', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile({
    required String displayName,
    String? photoUrl,
  }) async {
    try {
      _updateAuthProcess(AuthProcess.profileUpdate);

      dev.log('AuthService: Attempting profile update');

      final user = _auth.currentUser;
      if (user == null) {
        return AuthResult.failure('No user logged in');
      }

      // Update Firebase Auth profile
      await user.updateDisplayName(displayName);
      if (photoUrl != null) {
        await user.updatePhotoURL(photoUrl);
      }

      // Update Firestore
      try {
        await _firestore.collection('users').doc(user.uid).update({
          'displayName': displayName,
          if (photoUrl != null) 'photoUrl': photoUrl,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } catch (e) {
        dev.log('AuthService: Error updating user in Firestore: $e');
        // We can continue even if this fails
      }

      dev.log('AuthService: Profile update successful');
      return AuthResult.success(user);
    } catch (e) {
      dev.log('AuthService: Error during profile update: $e');
      return AuthResult.failure('Failed to update profile', e);
    } finally {
      _updateAuthProcess(AuthProcess.idle);
    }
  }

  // Check if user is signed in
  bool isSignedIn() {
    return currentUser != null;
  }

  // Get readable error message from Firebase Auth Exception
  String _getReadableErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'email-already-in-use':
        return 'This email is already in use by another account.';
      case 'weak-password':
        return 'The password is too weak.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with the same email but different sign-in credentials.';
      case 'invalid-credential':
        return 'The credentials are invalid.';
      case 'invalid-verification-code':
        return 'The verification code is invalid.';
      case 'invalid-verification-id':
        return 'The verification ID is invalid.';
      case 'requires-recent-login':
        return 'This operation requires recent authentication. Please log in again.';
      default:
        return e.message ?? 'An unknown error occurred.';
    }
  }

  // Clean up resources
  void dispose() {
    // No ValueNotifiers to dispose anymore
  }
}