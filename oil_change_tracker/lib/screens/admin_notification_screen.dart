import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../services/firebase_functions_service.dart';
import '../features/auth/data/providers/auth_provider.dart';

class AdminNotificationScreen extends HookConsumerWidget {
  const AdminNotificationScreen({Key? key}) : super(key: key);

  static const String adminEmail = '<EMAIL>';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final titleController = useTextEditingController();
    final bodyController = useTextEditingController();
    final notificationTypeValue = useState('promotional');
    final isLoading = useState(false);
    final resultMessage = useState<String?>(null);
    final authState = ref.watch(authStateChangesProvider);
    final sendingPhase = useState<String>('');
    final retryCount = useState(0);
    
    // Check if current user is admin
    final isAdmin = authState.value?.email == adminEmail;
    
    // Redirect non-admin users
    if (authState.value != null && !isAdmin) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Access denied. Admin privileges required.'),
            backgroundColor: Colors.red,
          ),
        );
        context.go('/home');
      });
    }
    
    // Show loading screen until auth state is determined
    if (authState.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    // Function to refresh all tokens using the improved service
    Future<bool> _refreshAllTokens() async {
      try {
        sendingPhase.value = 'Resetting authentication tokens...';
        retryCount.value++;
        
        // Pre-refresh auth token
        await FirebaseAuth.instance.currentUser?.getIdToken(true);
        
        // Allow a moment for token to propagate
        await Future.delayed(const Duration(seconds: 1));
        
        // Use the service to do a complete refresh
        final functionsService = ref.read(firebaseFunctionsServiceProvider);
        final success = await functionsService.refreshAllTokens();
        
        // Allow more time for tokens to propagate
        await Future.delayed(const Duration(seconds: 3));
        
        sendingPhase.value = '';
        return success;
      } catch (e) {
        print('Error refreshing tokens: $e');
        sendingPhase.value = '';
        return false;
      }
    }
    
    // Function to send notification with app check bypass
    Future<void> sendNotification() async {
      if (titleController.text.isEmpty || bodyController.text.isEmpty) {
        resultMessage.value = 'Title and body are required';
        return;
      }
      
      isLoading.value = true;
      resultMessage.value = 'Preparing to send notification...';
      
      try {
        // Phase 1: Refresh tokens (multiple times if needed)
        sendingPhase.value = 'Refreshing authentication tokens...';
        bool tokensRefreshed = await _refreshAllTokens();
        
        // If first refresh failed, try once more after waiting
        if (!tokensRefreshed && retryCount.value < 3) {
          sendingPhase.value = 'Retrying token refresh...';
          await Future.delayed(const Duration(seconds: 5));
          tokensRefreshed = await _refreshAllTokens();
        }
        
        // Phase 2: Send notification with fresh tokens
        sendingPhase.value = 'Sending notification...';
        await Future.delayed(const Duration(seconds: 1)); // Ensure UI updates
        
        final functionsService = ref.read(firebaseFunctionsServiceProvider);
        final success = await functionsService.sendTopicNotification(
          topic: 'all_users',
          title: titleController.text,
          body: bodyController.text,
          notificationType: notificationTypeValue.value,
        );
        
        sendingPhase.value = '';
        
        if (success) {
          resultMessage.value = 'Notification sent successfully to all users!';
          titleController.clear();
          bodyController.clear();
          retryCount.value = 0;
        } else {
          // Failed to send - provide detailed guidance
          resultMessage.value = 'Failed to send notification. Please try these steps in order:\n'
                              + '1. Click "Reset All Tokens" and wait 30 seconds\n'
                              + '2. Try sending again\n'
                              + '3. If still failing, restart the app and try again\n'
                              + '4. As a last resort, sign out and sign back in';
        }
      } catch (e) {
        sendingPhase.value = '';
        String errorMessage = e.toString();
        
        // Provide more helpful messages for common errors
        if (errorMessage.contains('unauthenticated') || 
            errorMessage.contains('permission-denied') ||
            errorMessage.contains('App attestation failed') ||
            errorMessage.contains('Too many attempts')) {
          resultMessage.value = 'Authentication error: App Check or Firebase Auth token issue.\n'
                            + 'Please try these steps:\n'
                            + '1. Click "Reset All Tokens" and wait 30 seconds\n'
                            + '2. Try sending again\n'
                            + '3. If still failing, restart the app and try again\n'
                            + '4. As a last resort, sign out and sign back in';
        } else {
          resultMessage.value = 'Error: ${e.toString()}';
        }
        
        print('Error sending notification: $e');
      } finally {
        isLoading.value = false;
        sendingPhase.value = '';
      }
    }
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Notifications'),
        backgroundColor: const Color(0xFFA04747),
      ),
      body: !isAdmin 
        ? const Center(
            child: Text(
              'Access denied. Admin privileges required.',
              style: TextStyle(fontSize: 18, color: Colors.red),
            ),
          )
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 16),
                const Text(
                  'Send Notification to All Users',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Authentication Info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Admin: ${FirebaseAuth.instance.currentUser?.email ?? "Not signed in"}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'If you see App Check errors, you may need to wait up to 30 seconds between attempts.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // Notification Type Selector
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Notification Type',
                    border: OutlineInputBorder(),
                  ),
                  value: notificationTypeValue.value,
                  items: const [
                    DropdownMenuItem(value: 'promotional', child: Text('Promotional')),
                    DropdownMenuItem(value: 'oil_change', child: Text('Oil Change')),
                    DropdownMenuItem(value: 'maintenance', child: Text('Maintenance')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      notificationTypeValue.value = value;
                    }
                  },
                ),
                const SizedBox(height: 16),
                
                // Title Input
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Notification Title',
                    border: OutlineInputBorder(),
                    hintText: 'Enter notification title',
                  ),
                ),
                const SizedBox(height: 16),
                
                // Body Input
                TextField(
                  controller: bodyController,
                  decoration: const InputDecoration(
                    labelText: 'Notification Body',
                    border: OutlineInputBorder(),
                    hintText: 'Enter notification message',
                  ),
                  maxLines: 4,
                ),
                const SizedBox(height: 24),
                
                // Send Button
                ElevatedButton(
                  onPressed: isLoading.value ? null : sendNotification,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFA04747),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: isLoading.value
                      ? SizedBox(
                          height: 24,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                height: 20, 
                                width: 20, 
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                sendingPhase.value.isNotEmpty ? sendingPhase.value : 'Processing...',
                                style: const TextStyle(fontSize: 16, color: Colors.white),
                              ),
                            ],
                          ),
                        )
                      : const Text(
                          'Send to All Users',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                ),
                const SizedBox(height: 16),
                
                // Reset All Tokens Button
                TextButton.icon(
                  onPressed: isLoading.value 
                    ? null 
                    : () async {
                        isLoading.value = true;
                        resultMessage.value = 'Resetting and refreshing all tokens...';
                        
                        // Reset the app check service and get fresh tokens
                        final success = await _refreshAllTokens();
                        
                        if (success) {
                          resultMessage.value = 'Authentication tokens reset and refreshed successfully! Wait 30 seconds before sending.';
                        } else {
                          resultMessage.value = 'Failed to refresh tokens. Try signing out and back in.';
                        }
                        
                        isLoading.value = false;
                      },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reset All Tokens'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Result Message
                if (resultMessage.value != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: resultMessage.value!.contains('success')
                          ? Colors.green.shade100
                          : (resultMessage.value!.contains('Sending') || 
                             resultMessage.value!.contains('Preparing') || 
                             resultMessage.value!.contains('Resetting'))
                              ? Colors.blue.shade100 
                              : Colors.red.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      resultMessage.value!,
                      style: TextStyle(
                        color: resultMessage.value!.contains('success')
                            ? Colors.green.shade800
                            : (resultMessage.value!.contains('Sending') || 
                               resultMessage.value!.contains('Preparing') || 
                               resultMessage.value!.contains('Resetting'))
                                ? Colors.blue.shade800 
                                : Colors.red.shade800,
                      ),
                    ),
                  ),
                
                // Support information
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'App Check Troubleshooting:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '1. Always click "Reset All Tokens" before sending a notification\n'
                        '2. Wait at least 30 seconds before attempting to send\n'
                        '3. Firebase App Check has strict rate limits - avoid repeated attempts\n'
                        '4. If you get "App attestation failed", your tokens may be invalidated\n'
                        '5. If all else fails, close and restart the app, then try again',
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }
} 