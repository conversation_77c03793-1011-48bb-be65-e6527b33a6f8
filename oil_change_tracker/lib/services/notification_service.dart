import 'dart:developer' as dev;
import 'dart:convert';
import 'dart:math';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../features/profile/providers/user_settings_provider.dart';
import '../features/auth/data/providers/auth_provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;

final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService(ref);
});

// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  dev.log('Handling a background message: ${message.messageId}');
  // No additional logic here as we can't access providers in background handler
}

class NotificationService {
  final Ref _ref;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  // Channel for promotional notifications
  final AndroidNotificationChannel _promotionalChannel = const AndroidNotificationChannel(
    'promotional_channel',
    'Promotional Notifications',
    description: 'Notifications about premium features and updates',
    importance: Importance.defaultImportance,
  );
  
  // Channel for oil change reminders
  final AndroidNotificationChannel _oilChangeChannel = const AndroidNotificationChannel(
    'oil_change_channel',
    'Oil Change Reminders',
    description: 'Critical reminders about oil changes',
    importance: Importance.high,
  );
  
  // Channel for maintenance reminders
  final AndroidNotificationChannel _maintenanceChannel = const AndroidNotificationChannel(
    'maintenance_channel',
    'Maintenance Reminders',
    description: 'Reminders about general maintenance',
    importance: Importance.high,
  );

  NotificationService(this._ref);

  Future<void> initialize() async {
    try {
      // Don't request notification permissions here, moved to separate method
      
      // Configure local notifications
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: false, // Don't request permissions on initialization
        requestBadgePermission: false,
        requestSoundPermission: false,
      );
      await _localNotifications.initialize(
        const InitializationSettings(android: androidSettings, iOS: iosSettings),
        onDidReceiveNotificationResponse: _handleNotificationTap,
      );
      
      // Create notification channels
      final platform = _localNotifications.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();
      await platform?.createNotificationChannel(_promotionalChannel);
      await platform?.createNotificationChannel(_oilChangeChannel);
      await platform?.createNotificationChannel(_maintenanceChannel);
      
      // Register background message handler
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      
      // Handle notification tap when app is in background but opened
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationOpenedApp);
      
      // Get the current permission status
      final notificationSettings = await _messaging.getNotificationSettings();
      if (notificationSettings.authorizationStatus == AuthorizationStatus.authorized) {
        // Only if already authorized, get FCM token and save it
        final token = await _messaging.getToken();
        if (token != null) {
          await _saveTokenToDatabase(token);
        }
        
        // Listen for token refreshes
        _messaging.onTokenRefresh.listen(_saveTokenToDatabase);
        
        // Subscribe to appropriate topics based on user settings
        await _updateTopicSubscriptions();
      }
    } catch (e) {
      dev.log('Error initializing notification service: $e');
    }
  }
  
  /// Request notification permissions explicitly
  /// This should be called after onboarding is complete
  Future<bool> requestNotificationPermissions() async {
    try {
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        announcement: false,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
      );
      
      dev.log('User granted notification permission: ${settings.authorizationStatus}');
      
      final isGranted = settings.authorizationStatus == AuthorizationStatus.authorized;
      
      if (isGranted) {
        // Now that we have permission, get the token and save it
        final token = await _messaging.getToken();
        if (token != null) {
          await _saveTokenToDatabase(token);
        }
        
        // Listen for token refreshes
        _messaging.onTokenRefresh.listen(_saveTokenToDatabase);
        
        // Subscribe to appropriate topics based on user settings
        await _updateTopicSubscriptions();
      }
      
      return isGranted;
    } catch (e) {
      dev.log('Error requesting notification permissions: $e');
      return false;
    }
  }
  
  Future<void> _updateTopicSubscriptions() async {
    final settingsAsync = _ref.read(userSettingsProvider);
    final authState = _ref.read(authStateChangesProvider);
    
    if (authState.value == null) return;
    
    // Always subscribe to all_users
    await _messaging.subscribeToTopic('all_users');
    
    // Subscribe to topics based on user settings
    settingsAsync.whenData((settings) async {
      if (settings == null) return;
      
      // Handle promotional notifications
      if (settings.promotionalNotifications) {
        await _messaging.subscribeToTopic('promotions');
      } else {
        await _messaging.unsubscribeFromTopic('promotions');
      }
      
      // Handle oil change reminders
      if (settings.oilChangeReminders) {
        await _messaging.subscribeToTopic('oil_changes');
      } else {
        await _messaging.unsubscribeFromTopic('oil_changes');
      }
      
      // Handle maintenance reminders
      if (settings.maintenanceReminders) {
        await _messaging.subscribeToTopic('maintenance');
      } else {
        await _messaging.unsubscribeFromTopic('maintenance');
      }
      
      // Handle mileage reminders
      if (settings.mileageReminders) {
        await _messaging.subscribeToTopic('mileage');
      } else {
        await _messaging.unsubscribeFromTopic('mileage');
      }
      
      // Update user segment topics
      await _updateUserSegmentTopics();
    });
  }
  
  // Updates topic subscriptions based on user segments
  Future<void> _updateUserSegmentTopics() async {
    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) return;
      
      // Get car count from Firestore to determine heavy_users
      final carsSnapshot = await FirebaseFirestore.instance
          .collection('cars')
          .where('userId', isEqualTo: user.id)
          .get();
      
      final carCount = carsSnapshot.docs.length;
      
      // User is considered a heavy user if they have 3+ cars
      if (carCount >= 3) {
        await _messaging.subscribeToTopic('heavy_users');
        dev.log('User subscribed to heavy_users topic (has $carCount cars)');
      } else {
        await _messaging.unsubscribeFromTopic('heavy_users');
      }
      
      // Get user document to check subscription status and last login
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.id)
          .get();
      
      if (userDoc.exists) {
        final userData = userDoc.data();
        
        // Handle premium users
        final isPremium = userData?['isPremium'] == true;
        if (isPremium) {
          await _messaging.subscribeToTopic('premium_users');
          dev.log('User subscribed to premium_users topic');
        } else {
          await _messaging.unsubscribeFromTopic('premium_users');
        }
        
        // Handle inactive users (30+ days since last login)
        final lastLoginTimestamp = userData?['lastLoginAt'] as Timestamp?;
        if (lastLoginTimestamp != null) {
          final lastLogin = lastLoginTimestamp.toDate();
          final daysInactive = DateTime.now().difference(lastLogin).inDays;
          
          if (daysInactive >= 30) {
            await _messaging.subscribeToTopic('inactive_users');
            dev.log('User subscribed to inactive_users topic (inactive for $daysInactive days)');
          } else {
            await _messaging.unsubscribeFromTopic('inactive_users');
          }
        }
        
        // Handle new users (created in the last 7 days)
        final createdAtTimestamp = userData?['createdAt'] as Timestamp?;
        if (createdAtTimestamp != null) {
          final createdAt = createdAtTimestamp.toDate();
          final daysSinceCreation = DateTime.now().difference(createdAt).inDays;
          
          if (daysSinceCreation <= 7) {
            await _messaging.subscribeToTopic('new_users');
            dev.log('User subscribed to new_users topic (account is $daysSinceCreation days old)');
          } else {
            await _messaging.unsubscribeFromTopic('new_users');
          }
        }
      }
    } catch (e) {
      dev.log('Error updating user segment topics: $e');
    }
  }
  
  // Force refresh all topic subscriptions (for manual calls)
  Future<void> refreshTopicSubscriptions() async {
    try {
      dev.log('Manually refreshing all topic subscriptions...');
      await _updateTopicSubscriptions();
      dev.log('Topic subscriptions refreshed successfully');
      return;
    } catch (e) {
      dev.log('Error refreshing topic subscriptions: $e');
    }
  }
  
  Future<void> _saveTokenToDatabase(String token) async {
    try {
      final user = _ref.read(currentUserProvider);
      
      // Log token for debugging
      dev.log('FCM Token received: $token');
      
      if (user == null) {
        dev.log('Warning: Cannot save FCM token - current user is null');
        
        // Try to get the auth state directly to check if it's just a timing issue
        final authState = _ref.read(authStateChangesProvider).value;
        if (authState == null) {
          dev.log('Error: Auth state is also null, delaying token save');
          
          // Schedule a retry after a short delay
          Future.delayed(const Duration(seconds: 5), () async {
            final retryUser = _ref.read(currentUserProvider);
            final retryAuth = _ref.read(authStateChangesProvider).value;
            
            if (retryUser != null) {
              dev.log('Retry successful - User is now available');
              await _saveTokenToDatabase(token);
            } else if (retryAuth != null) {
              dev.log('Using Firebase Auth user as fallback: ${retryAuth.uid}');
              // Save using auth user directly
              await _saveTokenWithAuthUser(token, retryAuth.uid);
            } else {
              dev.log('Retry failed - User still null after delay');
            }
          });
          return;
        } else {
          // Use auth state user as fallback
          dev.log('Using Firebase Auth user as fallback: ${authState.uid}');
          await _saveTokenWithAuthUser(token, authState.uid);
          return;
        }
      }
      
      final userId = user.id;
      if (userId == null || userId.isEmpty) {
        dev.log('Error: User id is null or empty');
        return;
      }
      
      dev.log('Saving FCM token to database for user: $userId');
      
      // Direct Firestore update without using repository
      await FirebaseFirestore.instance.collection('users').doc(userId).set({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      dev.log('FCM token saved successfully to users collection');
      
      // Also save to a dedicated tokens collection for easier querying
      await FirebaseFirestore.instance.collection('fcm_tokens').doc(userId).set({
        'token': token,
        'userId': userId,
        'deviceInfo': {
          'platform': defaultTargetPlatform.toString(),
          'updatedAt': FieldValue.serverTimestamp(),
        }
      }, SetOptions(merge: true));
      
      dev.log('FCM token also saved to dedicated fcm_tokens collection');
    } catch (e, stackTrace) {
      dev.log('Error saving FCM token: $e');
      dev.log('Stack trace: $stackTrace');
    }
  }
  
  // Helper method to save token when only auth user is available
  Future<void> _saveTokenWithAuthUser(String token, String userId) async {
    try {
      if (userId.isEmpty) {
        dev.log('Error: Auth user id is empty');
        return;
      }
      
      dev.log('Saving FCM token for auth user: $userId');
      
      // Direct Firestore update without using repository
      await FirebaseFirestore.instance.collection('users').doc(userId).set({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      
      // Also save to a dedicated tokens collection for easier querying
      await FirebaseFirestore.instance.collection('fcm_tokens').doc(userId).set({
        'token': token,
        'userId': userId,
        'deviceInfo': {
          'platform': defaultTargetPlatform.toString(),
          'updatedAt': FieldValue.serverTimestamp(),
        }
      }, SetOptions(merge: true));
      
      dev.log('FCM token saved with auth user fallback');
    } catch (e) {
      dev.log('Error saving FCM token with auth user: $e');
    }
  }
  
  void _handleForegroundMessage(RemoteMessage message) async {
    dev.log('Got a message whilst in the foreground!');
    dev.log('Message data: ${message.data}');
    
    final notification = message.notification;
    final android = message.notification?.android;
    
    if (notification != null && android != null) {
      // Determine channel based on notification type
      final channelId = _getChannelIdFromMessage(message);

      await _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channelId,
            channelId == _promotionalChannel.id 
                ? _promotionalChannel.name 
                : channelId == _oilChangeChannel.id
                    ? _oilChangeChannel.name
                    : _maintenanceChannel.name,
            channelDescription: channelId == _promotionalChannel.id 
                ? _promotionalChannel.description 
                : channelId == _oilChangeChannel.id
                    ? _oilChangeChannel.description
                    : _maintenanceChannel.description,
            icon: android.smallIcon ?? '@mipmap/ic_launcher',
            color: Colors.deepPurple,
            importance: channelId == _promotionalChannel.id
                ? Importance.defaultImportance
                : Importance.high,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: json.encode(message.data),
      );
    }
  }
  
  String _getChannelIdFromMessage(RemoteMessage message) {
    // Check message data or category to determine appropriate channel
    final type = message.data['type'] ?? '';
    
    switch (type) {
      case 'promotional':
        return _promotionalChannel.id;
      case 'oil_change':
        return _oilChangeChannel.id;
      case 'maintenance':
        return _maintenanceChannel.id;
      default:
        // Default to promotional for FCM console messages
        return _promotionalChannel.id;
    }
  }
  
  void _handleNotificationTap(NotificationResponse details) {
    if (details.payload == null) return;
    
    try {
      final data = json.decode(details.payload!) as Map<String, dynamic>;
      _navigateBasedOnPayload(data);
    } catch (e) {
      dev.log('Error handling notification tap: $e');
    }
  }
  
  void _handleNotificationOpenedApp(RemoteMessage message) {
    dev.log('Notification caused app to open from background state!');
    if (message.data.isNotEmpty) {
      _navigateBasedOnPayload(message.data);
    }
  }
  
  void _navigateBasedOnPayload(Map<String, dynamic> data) {
    // Parse the data and navigate accordingly
    final type = data['type'];
    final targetId = data['targetId'];
    final route = data['route'];
    
    // Use BuildContext-less navigation with GoRouter
    if (route != null) {
      GoRouter.of(navigatorKey.currentContext!).go(route);
      return;
    }
    
    // Or navigate based on type and targetId
    switch (type) {
      case 'oil_change':
        if (targetId != null) {
          GoRouter.of(navigatorKey.currentContext!).push('/cars/$targetId');
        } else {
          GoRouter.of(navigatorKey.currentContext!).push('/cars');
        }
        break;
      case 'maintenance':
        if (targetId != null) {
          GoRouter.of(navigatorKey.currentContext!).push('/maintenance/$targetId');
        } else {
          GoRouter.of(navigatorKey.currentContext!).push('/maintenance');
        }
        break;
      case 'promotional':
        // Navigate to premium features screen or similar
        GoRouter.of(navigatorKey.currentContext!).push('/premium');
        break;
      default:
        // Default to dashboard
        GoRouter.of(navigatorKey.currentContext!).go('/dashboard');
    }
  }
  
  // Global navigator key to be used for BuildContext-less navigation
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  // Method to manually retrieve and save FCM token - can be called from anywhere in the app
  Future<String?> getAndSaveFCMToken() async {
    try {
      dev.log('Attempting to get FCM token...');
      
      // Check if FirebaseMessaging is initialized
      try {
        final appOptions = await FirebaseMessaging.instance.getNotificationSettings();
        dev.log('Notification settings authorization status: ${appOptions.authorizationStatus}');
        
        // If notifications are not authorized, request permissions
        if (appOptions.authorizationStatus != AuthorizationStatus.authorized) {
          dev.log('Notifications not authorized, requesting permissions...');
          final settings = await _messaging.requestPermission(
            alert: true, 
            badge: true, 
            sound: true,
          );
          dev.log('New authorization status: ${settings.authorizationStatus}');
        }
      } catch (settingsError) {
        dev.log('Error checking notification settings: $settingsError');
      }
      
      // Try to get token
      final token = await _messaging.getToken();
      
      if (token == null || token.isEmpty) {
        dev.log('Failed to get FCM token - null or empty returned');
        
        // Try to diagnose the issue
        try {
          // Check Google Play Services status on Android
          if (defaultTargetPlatform == TargetPlatform.android) {
            dev.log('Platform is Android - check for Google Play Services');
          }
          
          // Check for APNs configuration on iOS
          if (defaultTargetPlatform == TargetPlatform.iOS) {
            dev.log('Platform is iOS - check for valid APN certificates');
          }
        } catch (e) {
          dev.log('Error during diagnosis: $e');
        }
        
        return null;
      }
      
      dev.log('FCM token retrieved: ${token.substring(0, min(10, token.length))}...');
      
      // Save the token to Firestore
      await _saveTokenToDatabase(token);
      
      return token;
    } catch (e) {
      dev.log('Error getting FCM token: $e');
      
      // More detailed error diagnostic
      try {
        if (e.toString().contains('MISSING_INSTANCEID_SERVICE')) {
          dev.log('Google Play Services may not be available or outdated');
        } else if (e.toString().contains('INVALID_SENDER')) {
          dev.log('Firebase project configuration issue - check google-services.json');
        }
      } catch (diagnosticError) {
        dev.log('Error in error diagnostics: $diagnosticError');
      }
      
      return null;
    }
  }
  
  // Method to get the FCM token without saving it
  Future<String?> getFCMToken() async {
    try {
      dev.log('Retrieving FCM token...');
      final token = await _messaging.getToken();
      
      if (token == null || token.isEmpty) {
        dev.log('Failed to get FCM token - null or empty returned');
        return null;
      }
      
      dev.log('FCM token retrieved: ${token.substring(0, min(10, token.length))}...');
      return token;
    } catch (e) {
      dev.log('Error getting FCM token: $e');
      return null;
    }
  }
  
  // Method to reset the FCM token
  Future<String?> resetFCMToken() async {
    try {
      dev.log('Resetting FCM token...');
      await _messaging.deleteToken();
      dev.log('FCM token deleted');
      
      // Get a new token
      final newToken = await _messaging.getToken();
      
      if (newToken == null || newToken.isEmpty) {
        dev.log('Failed to get new FCM token after reset');
        return null;
      }
      
      dev.log('New FCM token generated: ${newToken.substring(0, min(10, newToken.length))}...');
      
      // Save the new token
      await _saveTokenToDatabase(newToken);
      return newToken;
    } catch (e) {
      dev.log('Error resetting FCM token: $e');
      return null;
    }
  }
  
  // Method to send a local notification (for testing)
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String type = 'promotional',
    int? id,
  }) async {
    final channelId = type == 'promotional' 
        ? _promotionalChannel.id
        : type == 'oil_change'
            ? _oilChangeChannel.id
            : _maintenanceChannel.name;
    
    await _localNotifications.show(
      id ?? DateTime.now().millisecond,
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          channelId,
          type == 'promotional'
              ? _promotionalChannel.name
              : type == 'oil_change'
                  ? _oilChangeChannel.name
                  : _maintenanceChannel.name,
          channelDescription: type == 'promotional'
              ? _promotionalChannel.description
              : type == 'oil_change'
                  ? _oilChangeChannel.description
                  : _maintenanceChannel.description,
          importance: type == 'promotional'
              ? Importance.defaultImportance
              : Importance.high,
        ),
        iOS: const DarwinNotificationDetails(),
      ),
      payload: payload,
    );
  }
  
  // Method to manually subscribe a user to a specific topic
  Future<bool> subscribeToTopic(String topic) async {
    try {
      dev.log('Subscribing to topic: $topic');
      await _messaging.subscribeToTopic(topic);
      dev.log('Successfully subscribed to topic: $topic');
      return true;
    } catch (e) {
      dev.log('Error subscribing to topic: $e');
      return false;
    }
  }
  
  // Method to unsubscribe a user from a specific topic
  Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      dev.log('Unsubscribing from topic: $topic');
      await _messaging.unsubscribeFromTopic(topic);
      dev.log('Successfully unsubscribed from topic: $topic');
      return true;
    } catch (e) {
      dev.log('Error unsubscribing from topic: $e');
      return false;
    }
  }
  
  // Method to get all topics a user is subscribed to
  // Note: Firebase doesn't provide a direct API to get subscribed topics
  // This method returns the topics based on user settings
  List<String> getSubscribedTopics() {
    final List<String> topics = ['all_users'];
    final settingsAsync = _ref.read(userSettingsProvider);
    
    settingsAsync.whenData((settings) {
      if (settings == null) return;
      
      if (settings.promotionalNotifications) {
        topics.add('promotions');
      }
      
      if (settings.oilChangeReminders) {
        topics.add('oil_changes');
      }
      
      if (settings.maintenanceReminders) {
        topics.add('maintenance');
      }
      
      if (settings.mileageReminders) {
        topics.add('mileage');
      }
    });
    
    return topics;
  }
  
  // Add a method to show a local test notification
  Future<void> showLocalTestNotification({
    required String title,
    required String body,
    String? payload,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Create notification details
      const androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'oil_change_channel',  // Channel ID
        'Oil Change Reminders',  // Channel name
        channelDescription: 'Notifications for oil change reminders',
        importance: Importance.high,
        priority: Priority.high,
        color: Color(0xFFA04747), // AppColors.burgundy
        icon: '@mipmap/ic_launcher', // Use app icon instead of custom icon
      );
      
      const platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
      );
      
      // Construct a JSON payload with any additional data
      String notificationPayload = payload ?? '';
      if (additionalData != null && additionalData.isNotEmpty) {
        notificationPayload = jsonEncode({
          'action': 'NOTIFICATION_CLICK',
          'data': additionalData,
          'type': 'oil_change',
        });
      }
      
      // Show the notification
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),  // Unique ID
        title,
        body,
        platformChannelSpecifics,
        payload: notificationPayload,
      );
      
      dev.log('Local notification shown: $title');
      return;
    } catch (e) {
      dev.log('Error showing local notification: $e');
      rethrow;
    }
  }
  
  // Cancel a local notification by ID
  Future<void> cancelNotification(int id) async {
    try {
      await _localNotifications.cancel(id);
      dev.log('Cancelled local notification with ID: $id');
    } catch (e) {
      dev.log('Error cancelling notification with ID $id: $e');
    }
  }

  // Call this once in app startup (e.g., main.dart)
  static void initializeTimeZones() {
    tz.initializeTimeZones();
  }

  Future<void> scheduleLocalNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    String type = 'maintenance',
  }) async {
    await _localNotifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      NotificationDetails(
        android: AndroidNotificationDetails(
          type == 'promotional'
              ? _promotionalChannel.id
              : type == 'oil_change'
                  ? _oilChangeChannel.id
                  : _maintenanceChannel.id,
          type == 'promotional'
              ? _promotionalChannel.name
              : type == 'oil_change'
                  ? _oilChangeChannel.name
                  : _maintenanceChannel.name,
          channelDescription: type == 'promotional'
              ? _promotionalChannel.description
              : type == 'oil_change'
                  ? _oilChangeChannel.description
                  : _maintenanceChannel.description,
          importance: type == 'promotional'
              ? Importance.defaultImportance
              : Importance.high,
        ),
        iOS: const DarwinNotificationDetails(),
      ),
      androidScheduleMode: AndroidScheduleMode.exact,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
      matchDateTimeComponents: DateTimeComponents.dateAndTime,
    );
  }
} 