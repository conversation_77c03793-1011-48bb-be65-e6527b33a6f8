import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'iOS platform is not supported for this app.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'MacOS platform is not supported for this app.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'Windows platform is not supported for this app.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'Linux platform is not supported for this app.',
        );
      default:
        throw UnsupportedError(
          'Unknown platform $defaultTargetPlatform',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB-8g3o13i2zctPuq6jmsTGjkpDJDgE_Vk',
    appId: '1:952433993522:web:8663ed9879c1ecca26d92b',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
    authDomain: 'oiltracker-73d6c.firebaseapp.com',
    measurementId: 'G-HHRP184LPC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD0FWVWgn2X2y-d6B7JCTI5to7X-Q8wCxU',
    appId: '1:952433993522:android:ca6320b3da134fe226d92b',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'YOUR-IOS-API-KEY',
    appId: 'YOUR-IOS-APP-ID',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
    iosClientId: 'YOUR-IOS-CLIENT-ID',
    iosBundleId: 'com.maximummdeia.oil_change_tracker',
  );
} 