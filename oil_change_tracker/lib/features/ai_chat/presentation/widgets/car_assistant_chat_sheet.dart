import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../providers/chat_provider.dart';
import 'chat_bubble.dart';
import 'chat_composer.dart';
import 'quick_prompt_chips.dart';

class CarAssistantChatSheet extends ConsumerStatefulWidget {
  final ScrollController? externalScrollController;

  const CarAssistantChatSheet({super.key, this.externalScrollController});

  @override
  ConsumerState<CarAssistantChatSheet> createState() =>
      _CarAssistantChatSheetState();
}

class _CarAssistantChatSheetState extends ConsumerState<CarAssistantChatSheet> {
  late final ScrollController _scrollController;
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController = widget.externalScrollController ?? ScrollController();
  }

  @override
  void dispose() {
    if (widget.externalScrollController == null) {
      _scrollController.dispose();
    }
    _textController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendMessage(String message) {
    if (message.trim().isEmpty) return;

    ref.read(chatProvider.notifier).sendMessage(message.trim());
    _textController.clear();
    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final messages = ref.watch(chatProvider);
    final isLoading = ref.watch(chatLoadingProvider);

    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: AnimatedPadding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
        child: Column(
          children: [
            // Chat area (always scrollable)
            Expanded(
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  if (messages.isEmpty) ...[
                    Center(
                      child: Column(
                        children: [
                          Icon(Icons.chat_bubble_outline,
                              size: 64,
                              color:
                                  context.secondaryTextColor.withOpacity(0.5)),
                          const SizedBox(height: 16),
                          Text(l10n.chatEmptyTitle,
                              style: TextStyle(
                                  fontSize: 16,
                                  color: context.secondaryTextColor),
                              textAlign: TextAlign.center),
                          const SizedBox(height: 8),
                          Text(l10n.chatEmptySubtitle,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: context.secondaryTextColor
                                      .withOpacity(0.7)),
                              textAlign: TextAlign.center),
                          const SizedBox(height: 16),
                          QuickPromptChips(onPromptSelected: _sendMessage),
                          const SizedBox(height: 16),
                          TextButton.icon(
                            onPressed: () {
                              ref.read(chatProvider.notifier).clearChat();
                            },
                            icon: Icon(
                              Icons.refresh,
                              color: context.accentColor,
                              size: 20,
                            ),
                            label: Text(
                              l10n.clearChatTooltip,
                              style: TextStyle(
                                color: context.accentColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    for (final message in messages)
                      ChatBubble(
                        message: message,
                        isRTL: Directionality.of(context) == TextDirection.rtl,
                      ),
                  ],
                ],
              ),
            ),

            // Loading indicator
            if (isLoading)
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      l10n.assistantThinking,
                      style: TextStyle(
                        color: context.secondaryTextColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),

            // Message composer
            ChatComposer(
              controller: _textController,
              onSendMessage: _sendMessage,
            ),
          ],
        ),
      ),
    );
  }
}
