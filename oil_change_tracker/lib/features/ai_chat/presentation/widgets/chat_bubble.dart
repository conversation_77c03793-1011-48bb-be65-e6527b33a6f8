import 'package:flutter/material.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../models/chat_message.dart';
import 'package:intl/intl.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isRTL;

  const ChatBubble({
    super.key,
    required this.message,
    required this.isRTL,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.isUser;
    final timeFormat = DateFormat('HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: context.accentColor.withOpacity(0.1),
              child: Icon(
                Icons.smart_toy,
                size: 20,
                color: context.accentColor,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: isUser
                    ? context.accentColor
                    : context.containerBackgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(18),
                  topRight: const Radius.circular(18),
                  bottomLeft: isUser
                      ? const Radius.circular(18)
                      : const Radius.circular(4),
                  bottomRight: isUser
                      ? const Radius.circular(4)
                      : const Radius.circular(18),
                ),
                border: !isUser
                    ? Border.all(
                        color: context.accentColor.withOpacity(0.2),
                        width: 1,
                      )
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display content. If AI message, render markdown & selectable.
                  isUser
                      ? SelectableText(
                          message.content,
                          style: const TextStyle(
                              fontSize: 16, color: Colors.white, height: 1.4),
                        )
                      : MarkdownBody(
                          data: message.content,
                          selectable: true,
                          styleSheet:
                              MarkdownStyleSheet.fromTheme(Theme.of(context))
                                  .copyWith(
                            p: TextStyle(
                                fontSize: 16,
                                color: context.primaryTextColor,
                                height: 1.4),
                            strong: TextStyle(
                                fontSize: 16,
                                color: context.primaryTextColor,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                  const SizedBox(height: 4),
                  Text(
                    timeFormat.format(message.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: isUser
                          ? Colors.white.withOpacity(0.7)
                          : context.secondaryTextColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: context.accentColor.withOpacity(0.1),
              child: Icon(
                Icons.person,
                size: 20,
                color: context.accentColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
