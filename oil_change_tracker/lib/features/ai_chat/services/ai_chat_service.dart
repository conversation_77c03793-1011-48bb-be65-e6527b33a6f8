import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../../core/services/api_key_service.dart';
import 'dart:developer' as dev;

enum AIProvider { openrouter, openai }

class AiChatService {
  static const String _openrouterBaseUrl =
      'https://openrouter.ai/api/v1/chat/completions';
  static const String _openaiBaseUrl =
      'https://api.openai.com/v1/chat/completions';
  final ApiKeyService _apiKeyService;

  AiChatService(this._apiKeyService);

  Future<String> getChatResponse(String userMessage,
      {String? carId, String? contextSummary}) async {
    // Try OpenRouter (DeepSeek) first, then fallback to OpenAI
    String? response = await _tryProvider(
        AIProvider.openrouter, userMessage, carId, contextSummary);

    if (response != null) {
      return response;
    }

    // Fallback to OpenAI
    response = await _tryProvider(
        AIProvider.openai, userMessage, carId, contextSummary);

    if (response != null) {
      return response;
    }

    // If both fail, throw an error
    throw Exception(
        'فشل في الاتصال بخدمة الذكاء الاصطناعي. يرجى المحاولة مرة أخرى.');
  }

  Future<String?> _tryProvider(AIProvider provider, String userMessage,
      String? carId, String? contextSummary) async {
    try {
      String apiKey;
      String baseUrl;
      String model;

      switch (provider) {
        case AIProvider.openrouter:
          apiKey = _apiKeyService.getOpenRouterApiKey();
          baseUrl = _openrouterBaseUrl;
          model =
              'deepseek/deepseek-r1-0528:free'; // Free DeepSeek R1 model through OpenRouter
          break;
        case AIProvider.openai:
          apiKey = _apiKeyService.getOpenAIApiKey();
          baseUrl = _openaiBaseUrl;
          model = 'gpt-3.5-turbo';
          break;
      }

      if (apiKey.isEmpty) {
        dev.log('AiChatService: ${provider.name} API key not configured');
        return null;
      }

      // Build system prompt
      String systemPrompt =
          '''أنت خبير ميكانيكي سيارات متخصص في تشخيص الأعطال وإصلاح السيارات.
مهمتك هي مساعدة المستخدمين في:
- تشخيص مشاكل السيارات
- تقديم نصائح الصيانة
- شرح أسباب الأعطال المحتملة
- اقتراح الحلول المناسبة

قواعد مهمة:
1. أجب بلغة المستخدم نفسه؛ إذا كان السؤال بالعربية فاجب بالعربية الفصحى، وإن كان بلغة أخرى فاجب بتلك اللغة مع الحفاظ على الدقة والوضوح.
2. إذا طلب المستخدم أي شيء لا يتعلق بمشاكل السيارات أو صيانتها، فاعـتذر باحترام وقل له باختصار أنك مختص فقط في مشاكل السيارات ولا يمكنك المساعدة في مواضيع أخرى.
3. لا تقدّم أي محتوى خارج نطاق السيارات.
4. كن دقيقاً في التشخيص واطلب معلومات إضافية إذا لزم الأمر.''';

      if (contextSummary != null && contextSummary.isNotEmpty) {
        systemPrompt += '\n\nمعلومات المستخدم:\n$contextSummary';
      } else if (carId != null) {
        systemPrompt += '\n\nمعلومات السيارة الحالية: $carId';
      }

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
          if (provider == AIProvider.openrouter)
            'HTTP-Referer': 'https://oil-change-tracker.app',
          if (provider == AIProvider.openrouter)
            'X-Title': 'Oil Change Tracker',
        },
        body: json.encode({
          'model': model,
          'messages': [
            {
              'role': 'system',
              'content': systemPrompt,
            },
            {
              'role': 'user',
              'content': userMessage,
            },
          ],
          'max_tokens': 500,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final content = data['choices'][0]['message']['content'];
        dev.log(
            'AiChatService: Successfully got response from ${provider.name}');
        return content;
      } else {
        dev.log(
            'AiChatService: ${provider.name} API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      dev.log('AiChatService: Error with ${provider.name}: $e');
      return null;
    }
  }
}

// Provider for the AI chat service
final aiChatServiceProvider = Provider<AiChatService>((ref) {
  final apiKeyService = ref.watch(apiKeyServiceProvider);
  return AiChatService(apiKeyService);
});
