import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/providers/auth_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as dev;
import '../../../car_management/providers/car_provider.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../../../generated/app_localizations.dart';
import 'package:oil_change_tracker/core/theme/theme_extensions.dart';
import '../widgets/maintenance_photo_picker.dart';
import '../../data/repositories/maintenance_repository.dart';
import '../../../../features/voice_input/presentation/widgets/voice_mic_button.dart';
import '../../../../features/voice_input/models/voice_form_type.dart';
import '../../../../features/voice_input/services/voice_command_service.dart';
import '../../../../shared/widgets/custom_text_field.dart';

class AddMaintenanceScreen extends ConsumerStatefulWidget {
  final String? carId;

  /// Optional data extracted from voice command to pre-populate the form.
  final Map<String, dynamic>? voiceData;

  const AddMaintenanceScreen({
    super.key,
    this.carId,
    this.voiceData,
  });

  @override
  ConsumerState<AddMaintenanceScreen> createState() =>
      _AddMaintenanceScreenState();
}

class _AddMaintenanceScreenState extends ConsumerState<AddMaintenanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _mileageController = TextEditingController();
  final _costController = TextEditingController();
  final _serviceProviderController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _date = DateTime.now();
  String _selectedMaintenanceType = 'generalService';
  String? _selectedCarId;
  bool _isLoading = false;
  bool _isProcessingAi = false;

  // List to store selected photo files
  final List<File> _selectedPhotoFiles = [];

  // List to store temporary URLs for preview
  final List<String> _tempPhotoUrls = [];

  List<String> _getMaintenanceTypes() {
    return [
      'generalService',
      'brakeService',
      'engineService',
      'transmissionService',
      'tireService',
      'batteryService',
      'airConditioning',
      'electricalSystem',
      'suspension',
      'exhaustSystem',
      'fuelSystem',
      'coolingSystem',
      'regularMaintenance',
      'other'
    ];
  }

  String _getLocalizedMaintenanceType(String type, S l10n) {
    switch (type) {
      case 'generalService':
        return l10n.generalService;
      case 'brakeService':
        return l10n.brakeService;
      case 'engineService':
        return l10n.engineService;
      case 'transmissionService':
        return l10n.transmissionService;
      case 'tireService':
        return l10n.tireService;
      case 'batteryService':
        return l10n.batteryService;
      case 'airConditioning':
        return l10n.airConditioning;
      case 'electricalSystem':
        return l10n.electricalSystem;
      case 'suspension':
        return l10n.suspension;
      case 'exhaustSystem':
        return l10n.exhaustSystem;
      case 'fuelSystem':
        return l10n.fuelSystem;
      case 'coolingSystem':
        return l10n.coolingSystem;
      case 'regularMaintenance':
        return l10n.regularMaintenance;
      case 'other':
        return l10n.other;
      default:
        return l10n.generalService;
    }
  }

  @override
  void initState() {
    super.initState();

    _selectedCarId = widget.carId ?? widget.voiceData?['carId'];

    // Apply voice data immediately if available
    if (widget.voiceData != null && widget.voiceData!.isNotEmpty) {
      _applyVoiceData(widget.voiceData!);
    }

    if (_selectedCarId != null) {
      _initializeMileage();
    }
  }

  void _applyVoiceData(Map<String, dynamic> extractedData) {
    // Same mapping logic as in _handleVoiceInput
    if (!mounted) return;
    setState(() {
      if (extractedData['mileage'] != null) {
        _mileageController.text = extractedData['mileage'].toString();
      }
      if (extractedData['maintenanceType'] != null) {
        final typeStr = extractedData['maintenanceType'].toString();
        _selectedMaintenanceType = typeStr;
      }
      if (extractedData['description'] != null) {
        _descriptionController.text = extractedData['description'];
      }
      if (extractedData['cost'] != null) {
        _costController.text = extractedData['cost'].toString();
      }
      if (extractedData['serviceProvider'] != null) {
        _serviceProviderController.text = extractedData['serviceProvider'];
      }
      if (extractedData['notes'] != null) {
        _notesController.text = extractedData['notes'];
      }
      if (extractedData['date'] != null) {
        try {
          _date = DateTime.parse(extractedData['date']);
        } catch (_) {
          _date = DateTime.now();
        }
      }
    });
  }

  Future<void> _initializeMileage() async {
    if (_selectedCarId == null) return;

    final carAsync = await ref.read(carProvider(_selectedCarId!).future);
    if (carAsync != null && mounted) {
      setState(() {
        _mileageController.text = carAsync.currentMileage.toString();
      });
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _mileageController.dispose();
    _costController.dispose();
    _serviceProviderController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _handleVoiceInput(
      String transcription, Map<String, dynamic>? extractedData) {
    VLOG('ui', 'Voice input received for maintenance: "$transcription"');

    if (extractedData == null || extractedData.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).couldNotUnderstandCommand),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    // Apply extracted data to form fields
    setState(() {
      if (extractedData['mileage'] != null) {
        _mileageController.text = extractedData['mileage'].toString();
      }
      if (extractedData['maintenanceType'] != null) {
        final typeStr = extractedData['maintenanceType'].toString();
        // Map the string to the enum value
        try {
          _selectedMaintenanceType = typeStr;
        } catch (e) {
          _selectedMaintenanceType = 'other';
        }
      }
      if (extractedData['description'] != null) {
        _descriptionController.text = extractedData['description'];
      }
      if (extractedData['cost'] != null) {
        _costController.text = extractedData['cost'].toString();
      }
      if (extractedData['serviceProvider'] != null) {
        _serviceProviderController.text = extractedData['serviceProvider'];
      }
      if (extractedData['notes'] != null) {
        _notesController.text = extractedData['notes'];
      }
      if (extractedData['date'] != null) {
        try {
          _date = DateTime.parse(extractedData['date']);
        } catch (e) {
          // If parsing fails, use today's date
          _date = DateTime.now();
        }
      }
    });

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(S.of(context).success),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  // Handler for when a photo is selected
  void _handlePhotoSelected(File photoFile) {
    try {
      dev.log('AddMaintenanceScreen: Photo selected: ${photoFile.path}');

      // Verify the file exists and has a valid size
      if (!photoFile.existsSync()) {
        dev.log(
            'AddMaintenanceScreen: Selected photo file does not exist: ${photoFile.path}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: Photo file not found')),
        );
        return;
      }

      final fileSize = photoFile.lengthSync();
      if (fileSize <= 0) {
        dev.log(
            'AddMaintenanceScreen: Selected photo has invalid size: $fileSize bytes');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: Invalid photo file')),
        );
        return;
      }

      setState(() {
        _selectedPhotoFiles.add(photoFile);
        // Create a temporary URL for preview
        _tempPhotoUrls.add(photoFile.path);
      });
    } catch (e) {
      dev.log('AddMaintenanceScreen: Error handling selected photo: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error processing selected photo')),
      );
    }
  }

  Future<void> _saveMaintenance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCarId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).pleaseSelectCar),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Check authentication status with more detailed error handling
      User? currentUser;
      try {
        // First, check current auth state
        currentUser = FirebaseAuth.instance.currentUser;

        if (currentUser == null) {
          dev.log('AddMaintenanceScreen: No user currently signed in');

          // Check if we can silently reauth through provider
          final authState = ref.read(authStateChangesProvider).value;
          if (authState != null) {
            dev.log(
                'AddMaintenanceScreen: Found authenticated user in provider, using that');
            currentUser = authState;
          } else {
            throw Exception('User not authenticated. Please sign in again.');
          }
        }

        dev.log('AddMaintenanceScreen: Current user ID: ${currentUser.uid}');

        // Get current token (don't force refresh yet)
        String? currentToken = await currentUser.getIdToken(false);
        if (currentToken?.isEmpty ?? true) {
          dev.log('AddMaintenanceScreen: Empty token, forcing refresh');
          // Force refresh if token is empty
          await currentUser.getIdToken(true);
        }

        // Check auth state again after token operations
        if (FirebaseAuth.instance.currentUser == null) {
          dev.log(
              'AddMaintenanceScreen: User signed out during token operations');
          throw Exception('Authentication error. Please sign in again.');
        }

        dev.log('AddMaintenanceScreen: Authentication verified successfully');
      } catch (authError) {
        dev.log('AddMaintenanceScreen: Authentication error: $authError');
        if (mounted) {
          // Show a user-friendly error with option to sign in again
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authError.toString().contains('sign in again')
                  ? S.of(context).authenticationError
                  : '${S.of(context).authenticationError}: ${authError.toString().split('Exception:').last}'),
              backgroundColor: context.secondaryAccentColor,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: S.of(context).signInAgain,
                onPressed: () => context.go('/login'),
              ),
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      // Proceed with maintenance creation
      final now = DateTime.now();
      final userId = currentUser.uid;

      dev.log(
          'AddMaintenanceScreen: Creating maintenance with user ID: $userId');

      // Create maintenance record
      final maintenance = MaintenanceModel(
        carId: _selectedCarId ?? '',
        userId: userId,
        description: _descriptionController.text,
        maintenanceType: _selectedMaintenanceType,
        mileage: int.parse(_mileageController.text),
        cost: double.parse(
            _costController.text.isEmpty ? '0' : _costController.text),
        serviceProvider: _serviceProviderController.text,
        notes: _notesController.text,
        date: _date,
        createdAt: now,
        updatedAt: now,
      );

      dev.log('AddMaintenanceScreen: Sending maintenance data to provider');

      try {
        // Add the maintenance record using repository
        final maintenanceRepo = ref.read(maintenanceRepositoryProvider);
        // Set the localization object on the repository
        maintenanceRepo.setL10n(S.of(context));
        final addedMaintenance =
            await maintenanceRepo.addMaintenance(maintenance);

        // If maintenance was added successfully and there are photos to upload
        if (addedMaintenance != null &&
            addedMaintenance.id != null &&
            _selectedPhotoFiles.isNotEmpty) {
          dev.log(
              'AddMaintenanceScreen: Uploading ${_selectedPhotoFiles.length} photos');

          final maintenanceId = addedMaintenance.id!;

          // Upload each photo and get URLs
          final List<String> photoUrls = [];
          for (final photoFile in _selectedPhotoFiles) {
            try {
              // Verify file still exists before upload
              if (!photoFile.existsSync()) {
                dev.log(
                    'AddMaintenanceScreen: Photo file no longer exists: ${photoFile.path}');
                continue;
              }

              dev.log(
                  'AddMaintenanceScreen: Uploading photo: ${photoFile.path}');
              final photoUrl = await maintenanceRepo.uploadMaintenancePhoto(
                  photoFile, _selectedCarId!, maintenanceId);

              if (photoUrl != null) {
                photoUrls.add(photoUrl);
                dev.log(
                    'AddMaintenanceScreen: Photo uploaded successfully: $photoUrl');
              } else {
                dev.log(
                    'AddMaintenanceScreen: Failed to upload photo: ${photoFile.path}');
              }
            } catch (photoError) {
              dev.log(
                  'AddMaintenanceScreen: Error uploading photo: $photoError');
              // Continue with other photos
            }
          }

          // Update maintenance record with photo URLs if any were uploaded
          if (photoUrls.isNotEmpty) {
            try {
              final updatedMaintenance =
                  addedMaintenance.copyWith(photoUrls: photoUrls);
              await maintenanceRepo.updateMaintenance(updatedMaintenance);
              dev.log(
                  'AddMaintenanceScreen: Updated maintenance with ${photoUrls.length} photo URLs');
            } catch (updateError) {
              dev.log(
                  'AddMaintenanceScreen: Error updating maintenance with photos: $updateError');
            }
          }
        }

        dev.log('AddMaintenanceScreen: Maintenance added successfully');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(S.of(context).maintenanceAdded)),
          );
          context.pop(true);
        }
      } catch (providerError) {
        dev.log('AddMaintenanceScreen: Provider error: $providerError');

        // Handle specific error types
        if (providerError.toString().contains('authentication') ||
            providerError.toString().contains('auth') ||
            providerError.toString().contains('sign in again')) {
          // Authentication error - offer to sign in again
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(S.of(context).authenticationError),
                backgroundColor: context.secondaryAccentColor,
                duration: const Duration(seconds: 5),
                action: SnackBarAction(
                  label: S.of(context).signInAgain,
                  onPressed: () => context.go('/login'),
                ),
              ),
            );
          }
        } else {
          // Generic error handling
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    '${S.of(context).error}: ${providerError.toString().split('Exception:').last}'),
                backgroundColor: context.secondaryAccentColor,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }

        throw providerError; // Rethrow to be caught by the outer catch block
      }
    } catch (e) {
      dev.log('AddMaintenanceScreen: Error adding maintenance: $e');
      // Error already handled in inner try-catch
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final S l10n = S.of(context);
    final carsAsync = ref.watch(carsProvider);
    final locale = Localizations.localeOf(context);
    final languageCode = locale.languageCode == 'ar' ? 'ar-EG' : 'en-US';

    return Stack(
      children: [
        Scaffold(
          backgroundColor: context.containerBackgroundColor,
          appBar: AppBar(
            title: Text(
              l10n.addMaintenance,
              style: TextStyle(
                color: context.accentColor,
                fontWeight: FontWeight.bold,
                fontSize: 22,
              ),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: IconThemeData(color: context.accentColor),
          ),
          body: carsAsync.when(
            loading: () => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
              ),
            ),
            error: (error, _) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: context.secondaryAccentColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.errorLoadingCars,
                    style: TextStyle(
                      color: context.primaryTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => ref.refresh(carsProvider),
                    icon: const Icon(Icons.refresh),
                    label: Text(l10n.retry),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 3,
                    ),
                  ),
                ],
              ),
            ),
            data: (cars) {
              if (cars.isEmpty) {
                return Center(
                  child: Card(
                    margin: const EdgeInsets.all(24),
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.directions_car_outlined,
                            size: 72,
                            color: context.accentColor,
                          ),
                          const SizedBox(height: 20),
                          Text(
                            l10n.noCarsFound,
                            style: TextStyle(
                              color: context.accentColor,
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            l10n.addCarFirst,
                            style: TextStyle(
                              color: context.secondaryTextColor,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          ElevatedButton.icon(
                            onPressed: () => context.go('/cars/add'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: context.accentColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 32,
                                vertical: 14,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              elevation: 3,
                            ),
                            icon: const Icon(Icons.add),
                            label: Text(
                              l10n.addCar,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Set the initial car if not set
              if (_selectedCarId == null && cars.isNotEmpty) {
                _selectedCarId = cars.first.id;
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildFormFields(context, l10n),
                    ],
                  ),
                ),
              );

            },
          ),
          floatingActionButton: VoiceMicButton(
            formType: VoiceFormType.maintenance,
            languageCode: locale.languageCode == 'ar' ? 'ar-EG' : 'en-US',
            onComplete: _handleVoiceInput,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        ),
      ],
    );
  }

  Widget _buildFormFields(BuildContext context, S l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Car selection
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.directions_car,
                        color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.selectCar,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedCarId,
                  decoration: InputDecoration(
                    labelText: l10n.selectCar,
                    labelStyle: TextStyle(color: context.accentColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.3)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.accentColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    filled: true,
                    fillColor: context.containerBackgroundColor,
                  ),
                  dropdownColor: context.containerBackgroundColor,
                  style: TextStyle(color: context.primaryTextColor, fontSize: 16),
                  borderRadius: BorderRadius.circular(12),
                  icon: Icon(Icons.arrow_drop_down, color: context.accentColor),
                  items: ref.read(carsProvider).asData?.value.map((car) {
                        return DropdownMenuItem(
                          value: car.id,
                          child: Text(
                            '${car.year} ${car.make} ${car.model}',
                            style: TextStyle(color: context.primaryTextColor),
                          ),
                        );
                      }).toList() ??
                      [],
                  onChanged: (value) {
                    setState(() {
                      _selectedCarId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return l10n.pleaseSelectCar;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),

        // Maintenance type and details
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.build, color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.maintenanceType,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedMaintenanceType,
                  decoration: InputDecoration(
                    labelText: l10n.maintenanceType,
                    labelStyle: TextStyle(color: context.accentColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.3)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: context.accentColor.withOpacity(0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.accentColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    filled: true,
                    fillColor: context.containerBackgroundColor,
                  ),
                  dropdownColor: context.containerBackgroundColor,
                  style: TextStyle(color: context.primaryTextColor, fontSize: 16),
                  borderRadius: BorderRadius.circular(12),
                  icon: Icon(Icons.arrow_drop_down, color: context.accentColor),
                  items: _getMaintenanceTypes().map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(
                        _getLocalizedMaintenanceType(type, l10n),
                        style: TextStyle(color: context.primaryTextColor),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedMaintenanceType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Description field
                CustomTextField(
                  controller: _descriptionController,
                  labelText: l10n.maintenanceDescription,
                  prefixIcon: Icon(Icons.description,
                      color: context.accentColor.withOpacity(0.7)),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return l10n.maintenanceDescription;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),



        // Mileage and cost details
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.speed, color: context.accentColor, size: 22),
                    const SizedBox(width: 8),
                    Text(
                      l10n.currentMileage,
                      style: TextStyle(
                        color: context.accentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Mileage field
                CustomTextField(
                  controller: _mileageController,
                  labelText: l10n.currentMileage,
                  keyboardType: TextInputType.number,
                  prefixIcon: Icon(Icons.speed,
                      color: context.accentColor.withOpacity(0.7)),
                  suffixText: 'KM',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return l10n.pleaseEnterMileage;
                    }
                    final mileage = int.tryParse(value);
                    if (mileage == null) {
                      return l10n.invalidMileage;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Cost field
                CustomTextField(
                  controller: _costController,
                  labelText: l10n.cost,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  prefixIcon: Icon(Icons.attach_money,
                      color: context.accentColor.withOpacity(0.7)),
                  placeholder: l10n.pleaseEnterCost,
                ),
              ],
            ),
          ),
        ),



        // Service Provider Name
        const SizedBox(height: 16),
        CustomTextField(
          controller: _serviceProviderController,
          labelText: l10n.serviceProvider,
          keyboardType: TextInputType.text,
        ),

        _buildFormField(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.notes,
                style: TextStyle(
                  color: context.accentColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _notesController,
                style: TextStyle(color: context.primaryTextColor),
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: '${l10n.notes} (${l10n.optional})',
                  filled: true,
                  fillColor: context.accentColor.withOpacity(0.05),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: context.accentColor.withOpacity(0.5)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: context.accentColor),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  prefixIcon: Icon(Icons.note,
                      color: context.accentColor.withOpacity(0.7)),
                ),
              ),
            ],
          ),
        ),

        _buildFormField(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.date,
                style: TextStyle(
                  color: context.accentColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildDatePicker(context),
            ],
          ),
        ),

        // Receipt Photos
        _buildFormField(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.receiptPhotos,
                style: TextStyle(
                  color: context.accentColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              MaintenancePhotoPicker(
                photoUrls: _tempPhotoUrls,
                onPhotoSelected: _handlePhotoSelected,
                onPhotoRemoved: (path) {
                  final index = _tempPhotoUrls.indexOf(path);
                  if (index >= 0) {
                    setState(() {
                      // Remove from both arrays to keep them in sync
                      _tempPhotoUrls.removeAt(index);
                      _selectedPhotoFiles.removeAt(index);
                      dev.log(
                          'AddMaintenanceScreen: Removed photo at index $index: $path');
                    });
                  }
                },
                isEditing: true,
                showTitle: false,
                borderColor: context.accentColor.withOpacity(0.5),
                backgroundColor: context.containerBackgroundColor,
                borderRadius: 12.0,
              ),
            ],
          ),
        ),

        // Save button
        ElevatedButton(
          onPressed: _isLoading ? null : _saveMaintenance,
          style: ElevatedButton.styleFrom(
            backgroundColor: context.secondaryAccentColor,
            foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            elevation: 3,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.save),
              const SizedBox(width: 8),
              Text(
                _isLoading ? "Saving..." : l10n.save,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({required Widget child}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: child,
      ),
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    final l10n = S.of(context);
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: _date,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
          builder: (context, child) => Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.dark(
                primary: context.accentColor,
                onPrimary: context.primaryTextColor,
                surface: context.containerBackgroundColor,
                onSurface: context.primaryTextColor,
              ),
            ),
            child: child!,
          ),
        );
        if (picked != null) {
          setState(() => _date = picked);
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: context.containerBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: context.accentColor.withOpacity(0.5)),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: context.accentColor),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.date,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat.yMMMd().format(_date),
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(Icons.arrow_drop_down, color: context.accentColor),
          ],
        ),
      ),
    );
  }
}
