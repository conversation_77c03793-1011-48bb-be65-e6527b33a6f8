import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/utils/permission_handler.dart';
import '../../data/services/maintenance_photo_service.dart';
import 'dart:developer' as dev;
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';

/// Widget for selecting or capturing a maintenance receipt photo
class MaintenancePhotoPicker extends ConsumerWidget {
  final List<String> photoUrls;
  final Function(File) onPhotoSelected;
  final Function(String)? onPhotoRemoved;
  final bool isEditing;
  final bool showTitle;
  final bool showButtons;
  final Color? borderColor;
  final Color? backgroundColor;
  final double? borderRadius;

  const MaintenancePhotoPicker({
    super.key,
    required this.photoUrls,
    required this.onPhotoSelected,
    this.onPhotoRemoved,
    this.isEditing = false,
    this.showTitle = true,
    this.showButtons = true,
    this.borderColor,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            l10n.receiptPhotos,
            style: TextStyle(
              color: context.accentColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addReceiptPhotosDesc,
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // Photo selector buttons
        if (showButtons) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildPhotoButton(
                context,
                icon: Icons.camera_alt,
                label: l10n.camera,
                onPressed: () => _takePhoto(context, ref),
              ),
              const SizedBox(width: 16),
              _buildPhotoButton(
                context,
                icon: Icons.photo_library,
                label: l10n.gallery,
                onPressed: () => _pickFromGallery(context, ref),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
        
        // Photo preview grid
        if (photoUrls.isNotEmpty) ...[
          // Only show the "Photos (count)" title if showTitle is true
          if (showTitle) ...[
            Text(
              '${l10n.photos} (${photoUrls.length})',
              style: TextStyle(
                color: context.primaryTextColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
          ],
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: photoUrls.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _buildPhotoPreview(
                    context, 
                    photoUrls[index], 
                    onTap: () => _viewPhoto(context, photoUrls[index]),
                    onRemove: isEditing && onPhotoRemoved != null
                        ? () => onPhotoRemoved!(photoUrls[index])
                        : null,
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  /// Build a photo action button
  Widget _buildPhotoButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(borderRadius ?? 8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor ?? context.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
          border: Border.all(color: borderColor ?? context.accentColor.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, size: 20, color: context.accentColor),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: context.primaryTextColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a photo preview thumbnail
  Widget _buildPhotoPreview(
    BuildContext context, 
    String photoUrl, {
    required VoidCallback onTap,
    VoidCallback? onRemove,
  }) {
    final bool isLocalFile = _isLocalFilePath(photoUrl);
    
    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: borderColor ?? context.accentColor.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(borderRadius ?? 12),
              color: backgroundColor,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius ?? 12),
              child: isLocalFile
                ? _buildLocalImagePreview(photoUrl)
                : _buildNetworkImagePreview(context, photoUrl),
            ),
          ),
        ),
        if (onRemove != null)
          Positioned(
            top: 4,
            right: 4,
            child: InkWell(
              onTap: onRemove,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: context.secondaryAccentColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Build a preview for a network image with robust error handling
  Widget _buildNetworkImagePreview(BuildContext context, String photoUrl) {
    final l10n = S.of(context);
    
    return CachedNetworkImage(
      imageUrl: photoUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: context.accentColor.withOpacity(0.05),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        dev.log('Error loading image preview: $error, URL: $url');
        
        // Check if this is a network connectivity error
        if (error is SocketException) {
          return Container(
            color: context.accentColor.withOpacity(0.05),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.signal_wifi_off,
                  color: context.secondaryAccentColor,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  l10n.offlineMode,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        
        return Container(
          color: context.accentColor.withOpacity(0.05),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.broken_image,
                color: context.secondaryAccentColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                l10n.errorLoadingImage,
                style: TextStyle(
                  color: context.secondaryTextColor,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build a preview for a local image file
  Widget _buildLocalImagePreview(String localPath) {
    try {
      final file = File(localPath);
      if (!file.existsSync()) {
        dev.log('Local file does not exist: $localPath');
        return const Center(
          child: Icon(Icons.broken_image, color: Colors.red),
        );
      }
      
      return Image.file(
        file,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          dev.log('Error loading local image: $error, path: $localPath');
          return Center(
            child: Icon(Icons.error, color: context.secondaryAccentColor),
          );
        },
      );
    } catch (e) {
      dev.log('Error rendering local preview: $e, path: $localPath');
      return Center(
        child: Icon(Icons.error, color: Colors.red.shade400),
      );
    }
  }

  /// Helper method to determine if a path is a local file or a remote URL
  bool _isLocalFilePath(String path) {
    // Check if the path is a local file path
    return !path.startsWith('http') && 
           !path.startsWith('https') && 
           !path.startsWith('gs://');
  }

  /// View a photo in full-screen
  void _viewPhoto(BuildContext context, String photoUrl) {
    final l10n = S.of(context);
    final isLocalFile = _isLocalFilePath(photoUrl);
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              l10n.viewPhoto,
              style: const TextStyle(color: Colors.white),
            ),
          ),
          body: Container(
            color: Colors.black,
            child: Center(
              child: InteractiveViewer(
                minScale: 0.5,
                maxScale: 3.0,
                child: isLocalFile
                    ? Image.file(
                        File(photoUrl),
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.error_outline, color: Colors.white, size: 48),
                                const SizedBox(height: 16),
                                Text(
                                  l10n.errorLoadingImage,
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    : CachedNetworkImage(
                        imageUrl: photoUrl,
                        placeholder: (context, url) => const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        errorWidget: (context, url, error) {
                          // Check if this is a network connectivity error
                          if (error is SocketException) {
                            return Center(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(Icons.signal_wifi_off, color: Colors.white, size: 48),
                                  const SizedBox(height: 16),
                                  Text(
                                    l10n.networkError,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    l10n.checkInternetConnection,
                                    style: TextStyle(color: Colors.white.withOpacity(0.7)),
                                  ),
                                ],
                              ),
                            );
                          }
                          
                          return Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.error_outline, color: Colors.white, size: 48),
                                const SizedBox(height: 16),
                                Text(
                                  l10n.errorLoadingImage,
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Take a photo with the camera
  Future<void> _takePhoto(BuildContext context, WidgetRef ref) async {
    final l10n = S.of(context);
    
    try {
      dev.log('MaintenancePhotoPicker: Requesting camera permission');
      final hasPermission = await AppPermissionHandler.requestCameraAndStoragePermissions(context);
      
      if (!hasPermission) {
        dev.log('MaintenancePhotoPicker: Camera permission denied');
        // Permission denied message is already shown by permission handler
        return;
      }
      
      dev.log('MaintenancePhotoPicker: Camera permission granted, taking photo');
      final maintenancePhotoService = ref.read(maintenancePhotoServiceProvider);
      
      // Show a loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.takingPhoto),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      
      final photoFile = await maintenancePhotoService.takePhoto();
      
      if (photoFile == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.noPhotoTaken),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }
      
      if (context.mounted) {
        onPhotoSelected(photoFile);
      }
    } catch (e) {
      dev.log('MaintenancePhotoPicker: Error taking photo: $e');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.failedToTakePhoto),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    }
  }

  /// Pick an image from the gallery
  Future<void> _pickFromGallery(BuildContext context, WidgetRef ref) async {
    final l10n = S.of(context);
    
    try {
      dev.log('MaintenancePhotoPicker: Requesting storage permission');
      final hasPermission = await AppPermissionHandler.requestStoragePermission(context);
      
      if (!hasPermission) {
        dev.log('MaintenancePhotoPicker: Storage permission denied');
        // Permission denied message is already shown by permission handler
        return;
      }
      
      dev.log('MaintenancePhotoPicker: Storage permission granted, picking image');
      final maintenancePhotoService = ref.read(maintenancePhotoServiceProvider);
      
      // Show a loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.selectingPhoto),
            duration: const Duration(seconds: 1),
          ),
        );
      }
      
      final photoFile = await maintenancePhotoService.pickImage();
      
      if (photoFile == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.noPhotoSelected),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }
      
      if (context.mounted) {
        onPhotoSelected(photoFile);
      }
    } catch (e) {
      dev.log('MaintenancePhotoPicker: Error picking image: $e');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.failedToSelectPhoto),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    }
  }
} 