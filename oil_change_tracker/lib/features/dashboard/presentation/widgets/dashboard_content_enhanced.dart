import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import '../../../car_management/providers/car_provider.dart';
import '../../../auth/providers/auth_provider.dart';
import '../../../../shared/services/image_cache_service.dart';
import 'package:flutter/services.dart';
import '../../../../shared/widgets/image_carousel.dart';
import '../../../maintenance/providers/maintenance_provider.dart';
import '../../../../core/models/maintenance_model.dart';
import '../../../../core/providers/weather_provider.dart';
import 'dart:developer' as dev;
import '../../../../core/models/user_model.dart';
import '../../../../core/models/weather_model.dart';
import '../../../../core/services/location_service.dart';
import 'package:geolocator/geolocator.dart';
import '../../../profile/providers/license_provider.dart';
import '../screens/dashboard_screen.dart';
import '../../../subscription/providers/subscription_provider.dart';
import '../../../subscription/models/subscription_tier.dart';
import '../../../ai_usage/providers/ai_usage_providers.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../main_navigation/presentation/screens/main_navigation_screen.dart'
    show mainNavIndexProvider;

class DashboardContentEnhanced extends ConsumerStatefulWidget {
  final List<CarModel> cars;
  final String userName;
  final Function(CarModel)? onCarTap;
  final VoidCallback? onVoiceInput;
  final VoidCallback? onAiChat;

  const DashboardContentEnhanced({
    super.key,
    required this.cars,
    required this.userName,
    this.onCarTap,
    this.onVoiceInput,
    this.onAiChat,
  });

  @override
  ConsumerState<DashboardContentEnhanced> createState() =>
      _DashboardContentEnhancedState();
}

class _DashboardContentEnhancedState
    extends ConsumerState<DashboardContentEnhanced>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  int _currentCarIndex = 0;
  late PageController _pageController;
  // Store the last image URL to detect changes
  String? _lastImageUrl;
  // Store the profile image widget to prevent rebuilds
  Widget? _cachedProfileImage;
  // Cache the car carousel to prevent rebuilds
  Widget? _cachedCarCarousel;
  // Flag to track if page is changing to prevent extra refreshes
  final bool _isChangingPage = false;
  // Track the currently selected car ID for maintenance
  String? _selectedCarId;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..forward();

    // Initialize page controller with better settings for smoother scrolling
    _pageController = PageController(
      initialPage: 0,
      viewportFraction: 0.95,
      keepPage: true,
    );

    // Initialize last image URL
    // Correctly access AsyncValue data
    _lastImageUrl = ref.read(authProvider).asData?.value?.photoUrl;

    // Safely initialize selected car ID with the first car if available
    _initializeSelectedCar();
  }

  // Safely initialize the selected car with proper error handling
  void _initializeSelectedCar() {
    try {
      if (widget.cars.isNotEmpty) {
        for (final car in widget.cars) {
          if (car.id != null) {
            _selectedCarId = car.id;
            _currentCarIndex = widget.cars.indexOf(car);
            dev.log('Selected car initialized: ${car.make} ${car.model}');
            return;
          }
        }
      }
      // If we reach here, either the list is empty or no car has an id
      _selectedCarId = null;
      _currentCarIndex = 0;
    } catch (e) {
      dev.log('Error initializing selected car: $e');
      _selectedCarId = null;
      _currentCarIndex = 0;
    }
  }

  // Update selected car safely when page changes
  void _updateSelectedCar(int index) {
    try {
      if (widget.cars.isNotEmpty && index >= 0 && index < widget.cars.length) {
        final car = widget.cars[index];
        if (car.id != null) {
          setState(() {
            _currentCarIndex = index;
            _selectedCarId = car.id;
          });

          // Trigger the onCarTap callback when page changes
          if (widget.onCarTap != null) {
            widget.onCarTap!(car);
          }

          // Provide haptic feedback
          HapticFeedback.lightImpact();
        }
      }
    } catch (e) {
      dev.log('Error updating selected car: $e');
    }
  }

  // Helper method to clear image cache - only call this when profile data changes
  void _clearImageCache() {
    final imageCacheService = ref.read(imageCacheServiceProvider);
    dev.log('Selectively clearing image caches only when needed');
    // Don't clear all caches, as it causes image reloading issues
    // Correctly access AsyncValue data
    final currentUserPhotoUrl = ref.read(authProvider).asData?.value?.photoUrl;
    if (currentUserPhotoUrl != null && currentUserPhotoUrl.isNotEmpty) {
      imageCache.evict(NetworkImage(currentUserPhotoUrl));
    }
  }

  // Check if profile image URL has changed and needs a refresh
  bool _shouldRefreshProfileImage(String? currentImageUrl) {
    // Normalize null and empty strings to simplify comparison
    final normalizedCurrent =
        currentImageUrl?.isEmpty == true ? null : currentImageUrl;
    final normalizedLast =
        _lastImageUrl?.isEmpty == true ? null : _lastImageUrl;

    if (normalizedLast != normalizedCurrent) {
      dev.log(
          'Profile image URL changed from $_lastImageUrl to $currentImageUrl - refreshing');
      _lastImageUrl = currentImageUrl;
      return true;
    }
    return false;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  /// Public method to show the mileage update dialog for a specific car index
  void showUpdateMileageDialog(
      BuildContext context, WidgetRef ref, int carIndex) {
    if (!mounted || carIndex < 0 || carIndex >= widget.cars.length) {
      return;
    }

    setState(() {
      _currentCarIndex = carIndex;
    });

    // Use the shared implementation from dashboard_screen.dart
    final car = widget.cars[carIndex];
    showMileageUpdateDialog(context, ref, car);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    // Get time of day for proper greeting
    final hour = DateTime.now().hour;
    String greeting;

    if (hour < 12) {
      greeting = l10n.goodMorning;
    } else if (hour < 17) {
      greeting = l10n.goodAfternoon;
    } else {
      greeting = l10n.goodEvening;
    }

    // Weather summary text (small, beside greeting)
    final weatherAsync = ref.watch(weatherProvider);
    String? weatherSummary;
    weatherAsync.whenData((weather) {
      if (weather != null) {
        final cond = weather.current.condition.text;
        final temp = weather.current.tempC.round();
        weatherSummary = '$temp°C $cond';
      }
    });

    // Get user profile to properly display profile image
    final userAsync = ref.watch(authProvider);

    // Double-check cars data to ensure we don't have null or range errors
    final bool hasValidCars =
        widget.cars.isNotEmpty && widget.cars.any((car) => car.id != null);

    // If current index is out of bounds, reset it
    if (_currentCarIndex >= widget.cars.length && widget.cars.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        setState(() => _currentCarIndex = 0);
      });
    }

    // Make sure selected car ID is valid
    if (hasValidCars &&
        (_selectedCarId == null ||
            !widget.cars.any((car) => car.id == _selectedCarId))) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        setState(() {
          // Find first car with valid ID
          for (var car in widget.cars) {
            if (car.id != null) {
              _selectedCarId = car.id;
              break;
            }
          }
        });
      });
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Greeting and profile
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            greeting,
                            style: TextStyle(
                              fontSize: 16,
                              color: context.secondaryTextColor,
                            ),
                          ),
                          if (weatherSummary != null) ...[
                            const SizedBox(width: 6),
                            Text(
                              '· $weatherSummary',
                              style: TextStyle(
                                fontSize: 12,
                                color: context.secondaryTextColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.userName,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // License expiry info below user name
                      Consumer(
                        builder: (context, ref, child) {
                          final expiryDate = ref.watch(licenseExpiryProvider);
                          final licenseState =
                              ref.watch(licenseNotifierProvider);
                          final l10n = S.of(context);

                          if (licenseState.isLoading) {
                            return const SizedBox(
                              height: 4,
                            );
                          }

                          Widget buildExpiryText() {
                            if (expiryDate == null) {
                              return Text(
                                l10n.tapToSetExpiryDate,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: context.accentColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }

                            final daysLeft =
                                expiryDate.difference(DateTime.now()).inDays;
                            Color daysColor;
                            if (daysLeft < 0) {
                              daysColor = Colors.red;
                            } else if (daysLeft < 30) {
                              daysColor = Colors.orange;
                            } else {
                              daysColor = Colors.green;
                            }

                            final expiryText = daysLeft < 0
                                ? '${l10n.expired} - ${DateFormat('dd/MM/yyyy').format(expiryDate)}'
                                : '$daysLeft ${l10n.days} - ${DateFormat('dd/MM/yyyy').format(expiryDate)}';

                            return Text(
                              expiryText,
                              style: TextStyle(
                                fontSize: 12,
                                color: daysColor,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          }

                          return Row(
                            children: [
                              Icon(
                                Icons.drive_eta,
                                size: 12,
                                color: context.accentColor,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                l10n.driverLicense,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: context.primaryTextColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Expanded(child: buildExpiryText()),
                            ],
                          );
                        },
                      ),
                      // Add subscription status indicator
                      Consumer(
                        builder: (context, ref, child) {
                          final subscriptionState =
                              ref.watch(subscriptionProvider);
                          if (subscriptionState.hasActiveSubscription) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.verified,
                                    color: AppColors.gold,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    l10n.premium,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.gold,
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  _buildDashboardAIUsageBar(context, ref,
                                      width: 60),
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
                // Profile picture with premium crown
                Stack(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // switch to Profile tab in main navigation
                        ref.read(mainNavIndexProvider.notifier).state = 4;
                        context.go('/main');
                      },
                      child: userAsync.when(
                        data: (user) {
                          final userId = user?.id ?? '';
                          final photoUrl = user?.photoUrl;
                          // Always use the image cache service for consistent profile image handling
                          return Material(
                            elevation: 3,
                            shape: const CircleBorder(),
                            clipBehavior: Clip.antiAlias,
                            color: Colors.transparent,
                            child: CircleAvatar(
                              radius: 28,
                              backgroundColor:
                                  context.accentColor.withOpacity(0.2),
                              child: ClipOval(
                                child: ref
                                    .watch(imageCacheServiceProvider)
                                    .buildProfileImage(
                                      userId: userId,
                                      imageUrl: photoUrl,
                                      size: 56,
                                      forceRefresh:
                                          false, // Never force refresh on build
                                      backgroundColor:
                                          context.accentColor.withOpacity(0.2),
                                      foregroundColor: Colors.white,
                                    ),
                              ),
                            ),
                          );
                        },
                        loading: () => CircleAvatar(
                          radius: 28,
                          backgroundColor: context.accentColor.withOpacity(0.2),
                          child: Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: context.accentColor,
                              ),
                            ),
                          ),
                        ),
                        error: (_, __) => CircleAvatar(
                          radius: 28,
                          backgroundColor: context.accentColor.withOpacity(0.2),
                          child: Icon(
                            Icons.person,
                            color: context.accentColor,
                            size: 28,
                          ),
                        ),
                      ),
                    ),
                    // Premium crown for premium subscribers
                    Consumer(
                      builder: (context, ref, child) {
                        final subscriptionState =
                            ref.watch(subscriptionProvider);
                        if (subscriptionState.hasActiveSubscription) {
                          return Positioned(
                            top: -4,
                            right: -4,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: context.cardColor,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.workspace_premium,
                                color: AppColors.gold,
                                size: 18,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Car summary or empty state
          if (!hasValidCars)
            _buildNoCarsView(context)
          else ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCarCarousel(context, ref),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildMaintenanceAlerts(context),
            ),
          ],
          const SizedBox(height: 24),
          // Quick actions
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: _buildActionButtons(context, ref),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, UserModel? userProfile) {
    final l10n = S.of(context);
    final isRTL = Directionality.of(context) == TextDirection.RTL;

    // Get the time of day
    final hour = DateTime.now().hour;
    String greeting;

    if (hour < 12) {
      greeting = l10n.goodMorning;
    } else if (hour < 17) {
      greeting = l10n.goodAfternoon;
    } else {
      greeting = l10n.goodEvening;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greeting,
                  style: TextStyle(
                    fontSize: 16,
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  userProfile?.displayName ?? l10n.unknownUser,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              // switch to Profile tab in main navigation
              ref.read(mainNavIndexProvider.notifier).state = 4;
              context.go('/main');
            },
            child: Container(
              width: 55,
              height: 55,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                // border: Border.all(
                //   color: context.accentColor,
                //   width: 2.0,
                // ),
                // boxShadow: [
                //   BoxShadow(
                //     color: context.accentColor.withOpacity(0.4), // 0.4 opacity
                //     blurRadius: 6,
                //     spreadRadius: 1,
                //   ),
                // ],
              ),
              child: Center(
                child: ClipOval(
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors
                          .transparent, // Changed from context.secondaryAccentColor
                    ),
                    child: ref
                        .watch(imageCacheServiceProvider)
                        .buildProfileImage(
                          userId: userProfile?.id ?? '',
                          imageUrl: userProfile?.photoUrl,
                          size: 50,
                          backgroundColor: context.secondaryAccentColor,
                          foregroundColor: context.secondaryTextColor,
                          forceRefresh:
                              _shouldRefreshProfileImage(userProfile?.photoUrl),
                        ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarCarousel(BuildContext context, WidgetRef ref) {
    // Safety check - if cars list is empty, show no cars view instead
    if (widget.cars.isEmpty) {
      return _buildNoCarsView(context);
    }

    // Additional safety check - if current index is invalid, reset it
    if (_currentCarIndex >= widget.cars.length) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        setState(() {
          _currentCarIndex = 0;
          // If we need to reset _selectedCarId as well
          if (widget.cars.isNotEmpty && widget.cars[0].id != null) {
            _selectedCarId = widget.cars[0].id;
          }
        });
      });
    }

    final l10n = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.myCars,
              style: TextStyle(
                color: context.accentColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),
            TextButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Loading...'),
                    duration: const Duration(seconds: 1),
                    backgroundColor: context.accentColor,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                context.push('/cars/add');
              },
              style: TextButton.styleFrom(
                foregroundColor: context.accentColor,
                backgroundColor:
                    context.accentColor.withOpacity(0.1), // 0.1 opacity
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.add, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    l10n.addCar,
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 400, // Further reduced to avoid overflow
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.cars.length,
            onPageChanged: (index) {
              _updateSelectedCar(index);
            },
            itemBuilder: (context, index) {
              // Safety check for index bounds
              if (index < 0 || index >= widget.cars.length) {
                return Card(
                  elevation: 1,
                  margin:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16)),
                  color: context.cardColor,
                  surfaceTintColor: Colors.transparent,
                  child: Center(
                    child: Text(
                      'Error loading car details',
                      style: TextStyle(color: context.accentColor),
                    ),
                  ),
                );
              }

              final car = widget.cars[index];
              // Wrap with GestureDetector to track taps
              return InkWell(
                onTap: () {
                  if (car.id != null) {
                    // Show a loading indicator while navigating
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Loading...'),
                        duration: const Duration(seconds: 1),
                        backgroundColor: context.accentColor,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                    // Navigate to car details using the correct route format
                    context.push('/cars/${car.id}');
                  }
                },
                child: _buildCarStatusCard(context, car, ref),
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        if (widget.cars.length > 1)
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                widget.cars.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: _currentCarIndex == index ? 24 : 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 3),
                  decoration: BoxDecoration(
                    color: _currentCarIndex == index
                        ? context.accentColor
                        : context.accentColor.withOpacity(0.3), // 0.3 opacity
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCarStatusCard(
      BuildContext context, CarModel car, WidgetRef ref) {
    final l10n = S.of(context);
    final progress = car.oilChangeProgress;
    final isOverdue = car.isOilChangeDue;
    final imageCacheService = ref.read(imageCacheServiceProvider);

    // Validate and filter image URLs
    List<String> validImages = [];
    if (car.imageUrls != null && car.imageUrls!.isNotEmpty) {
      for (String url in car.imageUrls!) {
        String? validUrl = imageCacheService.validateAndFixUrl(url);
        if (validUrl != null) {
          validImages.add(validUrl);
        }
      }
    }
    if (validImages.isEmpty &&
        car.imageUrl != null &&
        car.imageUrl!.isNotEmpty) {
      String? validUrl = imageCacheService.validateAndFixUrl(car.imageUrl);
      if (validUrl != null) {
        validImages.add(validUrl);
      }
    }

    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor, // Use theme-aware card color
      surfaceTintColor: Colors.transparent,
      child: Column(
        children: [
          // Image Section
          SizedBox(
            height: 170,
            child: ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(16)),
              child: Stack(
                children: [
                  // Car image
                  Positioned.fill(
                    child: validImages.isNotEmpty
                        ? RepaintBoundary(
                            child: ImageCarousel(
                              networkImages: validImages,
                              enableAdd: false,
                              enableDelete: false,
                              showIndicator: validImages.length > 1,
                              autoPlay: false,
                              height: double.infinity,
                              imageFit: BoxFit.cover,
                              borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(16)),
                              key: ValueKey('car_carousel_${car.id}'),
                            ),
                          )
                        : Container(
                            color: context.isDarkMode
                                ? context.accentColor.withOpacity(0.05)
                                : context.containerBackgroundColor
                                    .withOpacity(0.8),
                            child: Center(
                              child: Icon(
                                Icons.directions_car,
                                color: context.accentColor,
                                size: 48,
                              ),
                            ),
                          ),
                  ),

                  // Overdue tag if needed
                  if (isOverdue)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.red.shade700,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.warning_amber_rounded,
                                color: Colors.white, size: 14),
                            const SizedBox(width: 4),
                            Text(
                              l10n.oilChangeOverdue,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Content Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Car Title
                  Text(
                    '${car.year} ${car.make} ${car.model}',
                    style: TextStyle(
                      color: context.primaryTextColor,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),

                  // License Expiry (if applicable)
                  if (car.licenseExpiryDate != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          Icon(Icons.event_available,
                              color: car.isLicenseNearingExpiry
                                  ? (car.daysUntilLicenseExpiry < 0
                                      ? Colors.red.shade700
                                      : Colors.orange.shade700)
                                  : context.secondaryTextColor,
                              size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${l10n.licenseExpiryDate}: ${DateFormat.yMMMd(l10n.localeName).format(car.licenseExpiryDate!)}',
                              style: TextStyle(
                                color: car.isLicenseNearingExpiry
                                    ? (car.daysUntilLicenseExpiry < 0
                                        ? Colors.red.shade700
                                        : Colors.orange.shade700)
                                    : context.secondaryTextColor,
                                fontSize: 14,
                                fontWeight: car.isLicenseNearingExpiry
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Mileage Info
                  Row(
                    children: [
                      Icon(Icons.speed, color: context.accentColor, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '${NumberFormat('#,###').format(car.currentMileage)} km',
                        style: TextStyle(
                          color: context.primaryTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () =>
                            showMileageUpdateDialog(context, ref, car),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.accentColor,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(l10n.update,
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Oil Change Progress
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.oil_barrel,
                            color: car.isOilChangeDue
                                ? Colors.red.shade700
                                : Colors.blue.shade700,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            car.isOilChangeDue
                                ? l10n.oilChangeOverdue
                                : l10n.oilConsumption,
                            style: TextStyle(
                              color: car.isOilChangeDue
                                  ? Colors.red.shade700
                                  : Colors.blue.shade700,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          _buildOilChangeMileageText(car, isOverdue, l10n),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: car.oilChangeProgress.clamp(0.0, 1.0),
                          backgroundColor: car.isOilChangeDue
                              ? Colors.red.shade100
                              : Colors.blue.shade100,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            car.isOilChangeDue
                                ? Colors.red.shade700
                                : Colors.blue.shade700,
                          ),
                          minHeight: 6,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOilChangeMileageText(CarModel car, bool isOverdue, S l10n) {
    if (isOverdue) {
      final overdue = car.kilometersUntilNextChange.abs();
      return Text(
        '${NumberFormat.decimalPattern().format(overdue)} km ${l10n.overdue}',
        style: TextStyle(
          color: Colors.red.shade500,
          fontSize: 12,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      return Text(
        l10n.kmRemaining(car.kilometersUntilNextChange),
        style: TextStyle(
          color: Colors.blue.shade500,
          fontSize: 12,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  Widget _buildMaintenanceAlerts(BuildContext context) {
    final l10n = S.of(context);
    final overdueCars = widget.cars.where((car) => car.isOilChangeDue).toList();

    // Build the maintenance summary card that shows total costs, etc.
    Widget buildMaintenanceSummaryCard(
        int maintenanceCount, String formattedTotalCost) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: context.cardColor,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () => _showCarSelectionForMaintenanceHistory(context),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 18,
                      backgroundColor: context.accentColor.withOpacity(0.1),
                      child: Icon(Icons.build_outlined,
                          color: context.accentColor, size: 18),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      l10n.maintenance,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: context.primaryTextColor,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$maintenanceCount',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: context.accentColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Divider(color: context.accentColor.withOpacity(0.3), height: 1),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.totalMaintenanceCost,
                          style: TextStyle(
                            fontSize: 12,
                            color: context.secondaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${l10n.currencySymbol}$formattedTotalCost',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    ElevatedButton.icon(
                      onPressed: () => _showCarSelectionForMaintenance(context),
                      icon: const Icon(Icons.add, size: 16),
                      label: Text(l10n.add),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.accentColor,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Common maintenance data fetch function
    Widget buildMaintenanceData() {
      return Consumer(
        builder: (context, ref, child) {
          final maintenanceDataFutures = widget.cars.map((car) {
            if (car.id != null) {
              return ref.watch(maintenanceProvider(car.id!).future);
            } else {
              return Future.value(<MaintenanceModel>[]);
            }
          }).toList();

          return FutureBuilder<List<List<MaintenanceModel>>>(
            future: Future.wait(maintenanceDataFutures),
            builder: (context, snapshot) {
              int maintenanceCount = 0;
              double totalCost = 0.0;

              if (snapshot.hasData) {
                final allMaintenance =
                    snapshot.data!.expand((list) => list).toList();
                maintenanceCount = allMaintenance.length;
                totalCost = allMaintenance.fold<double>(
                    0, (prev, maint) => prev + maint.cost);
              }

              final formattedTotalCost =
                  NumberFormat.currency(symbol: '\$', decimalDigits: 2)
                      .format(totalCost);
              return buildMaintenanceSummaryCard(
                  maintenanceCount, formattedTotalCost);
            },
          );
        },
      );
    }

    // For the case where we only show a single car's maintenance
    Widget buildSelectedCarMaintenance() {
      // Safety check: if there are no cars, just show generic maintenance data
      if (widget.cars.isEmpty) {
        return buildMaintenanceData();
      }

      if (_selectedCarId != null) {
        final maintenanceAsync =
            ref.watch(maintenanceProvider(_selectedCarId!));

        return maintenanceAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) =>
              _buildMaintenanceErrorCard(context, error),
          data: (maintenance) {
            // Find the selected car with proper error handling
            CarModel? selectedCar;
            try {
              // Try to find the car with the selected ID
              for (var car in widget.cars) {
                if (car.id == _selectedCarId) {
                  selectedCar = car;
                  break;
                }
              }

              // If not found, use the first car if available
              if (selectedCar == null && widget.cars.isNotEmpty) {
                selectedCar = widget.cars[0];
              }
            } catch (e) {
              dev.log('Error finding selected car: $e');
              // If any error occurs, try using the first car if available
              if (widget.cars.isNotEmpty) {
                selectedCar = widget.cars[0];
              }
            }

            // Additional null check
            if (selectedCar == null) {
              return buildMaintenanceData();
            }

            final maintenanceCount = maintenance.length;
            final totalCost = maintenance.fold<double>(
                0, (prev, record) => prev + record.cost);
            final formattedTotalCost =
                NumberFormat.currency(symbol: '\$', decimalDigits: 2)
                    .format(totalCost);

            final isOilChangeDue =
                selectedCar.kilometersUntilNextChange <= 500 ||
                    selectedCar.daysUntilNextChange <= 7;
            final isOilChangeOverdue =
                selectedCar.kilometersUntilNextChange <= 0 ||
                    selectedCar.daysUntilNextChange <= 0;

            // Show both maintenance and oil change widgets when needed
            if (isOilChangeOverdue || isOilChangeDue) {
              return Column(
                children: [
                  _buildOilChangeDueCard(
                      context, selectedCar, isOilChangeOverdue),
                  const SizedBox(height: 16),
                  _buildRegularMaintenanceCard(context, selectedCar,
                      maintenanceCount, formattedTotalCost),
                ],
              );
            } else {
              return _buildRegularMaintenanceCard(
                  context, selectedCar, maintenanceCount, formattedTotalCost);
            }
          },
        );
      } else {
        return buildMaintenanceData();
      }
    }

    // No overdue cars, show single car maintenance or full maintenance summary
    if (overdueCars.isEmpty) {
      return buildSelectedCarMaintenance();
    }

    // Overdue cars section plus maintenance summary
    return Column(
      children: [
        // First card: Overdue cars alert
        Card(
          elevation: 1,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          color: context.cardColor,
          surfaceTintColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.red.withOpacity(0.1),
                      child: Icon(Icons.warning_amber_rounded,
                          color: Colors.red[700], size: 18),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      l10n.upcomingMaintenance,
                      style: TextStyle(
                        color: Colors.red[700],
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Divider(color: context.accentColor.withOpacity(0.3), height: 1),
                const SizedBox(height: 12),
                ...overdueCars
                    .take(2)
                    .map((car) => _buildAlertCard(context, car)),
                if (overdueCars.length > 2)
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      onPressed: () => context.push('/cars'),
                      icon: Icon(Icons.arrow_forward,
                          color: context.accentColor, size: 16),
                      label: Text(
                        l10n.viewAll,
                        style: TextStyle(
                          color: context.accentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),

        // Second card: Maintenance summary (non-duplicated)
        const SizedBox(height: 16),
        buildMaintenanceData(),
      ],
    );
  }

  Widget _buildAlertCard(BuildContext context, CarModel car) {
    final l10n = S.of(context);

    return Card(
      elevation: 0,
      color: Colors.red.withOpacity(0.05),
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: () {
          if (car.id != null) {
            context.push('/oil-changes/add?carId=${car.id}');
          } else {
            context.push('/oil-changes/add');
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Car icon
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.red.withOpacity(0.1),
                child: Icon(
                  Icons.directions_car,
                  color: Colors.red[700],
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),

              // Car details - center column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Car make/model with ellipsis
                    Text(
                      '${car.year} ${car.make} ${car.model}',
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Mileage info with ellipsis
                    Row(
                      children: [
                        Icon(Icons.oil_barrel,
                            color: Colors.red[700], size: 12),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            '${NumberFormat.decimalPattern().format(car.kilometersUntilNextChange.abs())} km ${l10n.overdue}',
                            style: TextStyle(
                              color: Colors.red[700],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action button - compact design
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: OutlinedButton(
                  onPressed: () {
                    if (car.id != null) {
                      context.push('/oil-changes/add?carId=${car.id}');
                    } else {
                      context.push('/oil-changes/add');
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.accentColor,
                    side: BorderSide(color: context.accentColor),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    minimumSize: const Size(0, 30),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    l10n.add,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMaintenanceErrorCard(BuildContext context, Object error) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor, // Use theme-aware card color
      surfaceTintColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.red.withOpacity(0.1),
              child: Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error Loading Maintenance',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap to retry',
                    style: TextStyle(
                      fontSize: 14,
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegularMaintenanceCard(BuildContext context, CarModel car,
      int maintenanceCount, String formattedTotalCost) {
    final l10n = S.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor, // Use theme-aware card color
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Navigate to maintenance history for the selected car
          if (car.id != null) {
            context.push('/cars/${car.id}/maintenance');
          }
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 18,
                    backgroundColor: context.accentColor.withOpacity(0.1),
                    child: Icon(
                      Icons.build_outlined,
                      color: context.accentColor,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${car.make} ${car.model} ${l10n.maintenance}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: context.primaryTextColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$maintenanceCount',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: context.accentColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Divider(
                  color: context.accentColor.withOpacity(0.3),
                  height: 1), // 0.3 opacity
              const SizedBox(height: 12),
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.cost,
                        style: TextStyle(
                          fontSize: 12,
                          color: context.secondaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        formattedTotalCost,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: () {
                      if (car.id != null) {
                        context.push('/cars/${car.id}/maintenance/add');
                      }
                    },
                    icon: const Icon(Icons.add, size: 16),
                    label: Text(l10n.addNew),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOilChangeDueCard(
      BuildContext context, CarModel car, bool isOverdue) {
    final l10n = S.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor, // Use theme-aware card color
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (car.id != null) {
            context.push('/oil-changes/add?carId=${car.id}');
          }
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header banner
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
              decoration: BoxDecoration(
                color: isOverdue ? Colors.red[700] : Colors.amber[700],
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    isOverdue ? Icons.warning_amber_rounded : Icons.access_time,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isOverdue ? l10n.oilChangeOverdue : l10n.oilChangeDueSoon,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),

            // Content section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Car info row
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          radius: 18,
                          backgroundColor:
                              (isOverdue ? Colors.red : Colors.amber)
                                  .withOpacity(0.1),
                          child: Icon(
                            Icons.oil_barrel,
                            color:
                                isOverdue ? Colors.red[700] : Colors.amber[700],
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${car.make} ${car.model}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: context.primaryTextColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              _buildOilChangeMileageText(
                                  car, isOverdue, S.of(context)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Action button - made more compact to prevent overflow
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        if (car.id != null) {
                          context.push('/oil-changes/add?carId=${car.id}');
                        }
                      },
                      icon: const Icon(Icons.add, size: 14),
                      label: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          l10n.recordOilChange,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isOverdue ? Colors.red[700] : Colors.amber[700],
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 8),
                        minimumSize: const Size(0, 36),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.quickActions,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 130, // Increased height to avoid overflow
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildActionButton(
                  context: context,
                  icon: Icons.directions_car,
                  label: l10n.myCars,
                  onTap: () => widget.cars.isNotEmpty
                      ? context.push('/cars')
                      : ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.addCarFirst))),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 1,
                child: _buildActionButton(
                  context: context,
                  icon: Icons.oil_barrel,
                  label: l10n.recordOilChange,
                  onTap: () => widget.cars.isNotEmpty
                      ? context.push('/oil-changes/add')
                      : ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.addCarFirst))),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 1,
                child: _buildActionButton(
                  context: context,
                  icon: Icons.build,
                  label: l10n.maintenance,
                  onTap: () => widget.cars.isNotEmpty
                      ? context.push('/maintenance/add')
                      : ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(l10n.addCarFirst))),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 130, // Increased height to avoid overflow
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: _buildActionButton(
                  context: context,
                  icon: Icons.settings,
                  label: l10n.settings,
                  onTap: () => context.push('/settings'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(flex: 1, child: Container()), // Empty placeholder
              const SizedBox(width: 16),
              Expanded(flex: 1, child: Container()), // Empty placeholder
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor, // Use theme-aware card color
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: context.accentColor,
                  size: 18,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: context.primaryTextColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.visible,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherWidget(BuildContext context) {
    final weatherAsync = ref.watch(weatherProvider);
    final l10n = S.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: context.cardColor,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () => _showWeatherOptions(context),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: weatherAsync.when(
            data: (weather) {
              if (weather == null) {
                return _buildWeatherError(context, l10n, hasError: false);
              }
              return _buildWeatherData(context, weather);
            },
            loading: () => _buildWeatherLoading(),
            error: (error, stack) {
              dev.log('Weather error: $error');
              return _buildWeatherError(context, l10n, hasError: true);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherData(BuildContext context, WeatherModel weather) {
    // No need for l10n here as we're using the weather data directly
    final textTheme = Theme.of(context).textTheme;

    // Get condition text, and ensure first letter is capitalized
    final conditionText = weather.current.condition.text;
    final capitalizedCondition = conditionText.isNotEmpty
        ? '${conditionText[0].toUpperCase()}${conditionText.substring(1)}'
        : '';

    return SizedBox(
      height: 50, // Fixed height to match license widget
      child: Row(
        children: [
          Image.network(
            'https:${weather.current.condition.icon}',
            width: 30,
            height: 30,
            errorBuilder: (context, error, stackTrace) => Icon(
              Icons.cloud,
              color: Colors.amber,
              size: 24,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${weather.location.name}',
                  style: textTheme.bodySmall?.copyWith(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  capitalizedCondition,
                  style: textTheme.bodySmall?.copyWith(
                    color: context.secondaryTextColor,
                    fontSize: 9,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '${weather.current.tempC.round()}°C',
            style: textTheme.titleMedium?.copyWith(
              color: context.primaryTextColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherLoading() {
    return SizedBox(
      height: 50, // Fixed height to match license widget
      child: Row(
        children: [
          const SizedBox(
            width: 28,
            height: 28,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.amber,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 100,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1), // 0.1 opacity
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 3),
                Container(
                  width: 70,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1), // 0.1 opacity
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherError(BuildContext context, S l10n,
      {required bool hasError}) {
    return SizedBox(
      height: 50, // Fixed height to match license widget
      child: Row(
        children: [
          Icon(
            hasError ? Icons.cloud_off : Icons.location_off,
            color: Colors.amber,
            size: 26,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  hasError ? l10n.weatherUnavailable : l10n.locationNeeded,
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 11,
                  ),
                ),
                Text(
                  hasError ? l10n.checkConnection : l10n.tapToSetLocation,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 9,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              hasError ? Icons.refresh : Icons.add_location_alt_outlined,
              color: Colors.amber,
              size: 18,
            ),
            padding: EdgeInsets.zero,
            onPressed: () {
              if (hasError) {
                // Refresh weather
                ref.refresh(weatherRefreshProvider.notifier).state++;
              } else {
                // Request location permission
                _requestLocationPermission(context);
              }
            },
          ),
        ],
      ),
    );
  }

  Future<void> _requestLocationPermission(BuildContext context) async {
    try {
      // Get the location service with null safety check
      final locationService = ref.read(locationServiceProvider);

      // Store l10n in a variable to avoid multiple context accesses
      // Check if context is still valid before accessing
      if (!mounted) return;
      final l10n = S.of(context);

      // First check if location services are enabled
      bool? servicesEnabled;
      try {
        servicesEnabled = await locationService.isLocationServiceEnabled();
      } catch (e) {
        dev.log('Error checking location services: $e');
        servicesEnabled = false;
      }

      if (servicesEnabled != true) {
        // Explicitly check for true
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.locationNeeded),
            backgroundColor: context.secondaryAccentColor,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: l10n.openSettings,
              onPressed: () {
                try {
                  Geolocator.openLocationSettings();
                } catch (e) {
                  dev.log('Error opening location settings: $e');
                  // Show fallback message if settings can't be opened
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(l10n.locationNeeded),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
            ),
          ),
        );
        return;
      }

      // Request permission with proper error handling
      LocationPermission? permission;
      try {
        permission = await locationService.requestPermission();
      } catch (e) {
        dev.log('Error requesting location permission: $e');
        permission = LocationPermission.denied; // Default to denied on error
      }

      // Use safe comparison to prevent null reference errors
      if (!mounted) return;

      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        try {
          // Set preference to use location-based weather
          ref.read(useLocationForWeatherProvider.notifier).state = true;

          // Refresh weather
          ref.refresh(weatherRefreshProvider.notifier).state++;
        } catch (e) {
          dev.log('Error updating weather providers: $e');
          // Continue showing success message anyway
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.locationPermissionGranted),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else if (permission == LocationPermission.denied) {
        // Use if check for mounted again to be extra safe
        if (mounted) {
          _showLocationRationale(context);
        }
      } else if (permission == LocationPermission.deniedForever) {
        if (mounted) {
          _showPermissionSettings(context);
        }
      } else {
        // Handle undefined or null permission state
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.locationPermissionRationale),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      // Global error handling to prevent crashes
      dev.log('Uncaught error in _requestLocationPermission: $e');
      dev.log('Stack trace: $stackTrace');

      // Show error to user if context is still valid
      if (mounted) {
        final l10n = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.locationNeeded),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showLocationRationale(BuildContext context) {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.locationNeeded,
          style: TextStyle(color: context.primaryTextColor),
        ),
        content: Text(
          l10n.locationPermissionRationale,
          style: TextStyle(color: context.secondaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.cancel,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _requestLocationPermission(context);
            },
            child: Text(
              l10n.locationPermissionGranted,
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showPermissionSettings(BuildContext context) {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.locationNeeded,
          style: TextStyle(color: context.primaryTextColor),
        ),
        content: Text(
          l10n.locationPermissionRationale,
          style: TextStyle(color: context.secondaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.cancel,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Geolocator.openAppSettings();
            },
            child: Text(
              l10n.openSettings,
              style: TextStyle(color: context.secondaryAccentColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showWeatherOptions(BuildContext context) {
    final l10n = S.of(context);

    showModalBottomSheet(
      context: context,
      backgroundColor: context.containerBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.weatherOptions,
              style: TextStyle(
                color: context.primaryTextColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Divider(
                color: context.accentColor.withOpacity(0.3),
                height: 1), // 0.3 opacity
            const SizedBox(height: 12),
            _buildOptionTile(
              context,
              icon: Icons.location_on,
              title: l10n.useDeviceLocation,
              subtitle: l10n.locationExplanation,
              onTap: () {
                Navigator.pop(context);
                _requestLocationPermission(context);
              },
            ),
            const SizedBox(height: 12),
            _buildOptionTile(
              context,
              icon: Icons.language,
              title: l10n.useIpLocation,
              subtitle: l10n.ipLocationExplanation,
              onTap: () {
                Navigator.pop(context);
                // Set preference to NOT use location-based weather
                ref.read(useLocationForWeatherProvider.notifier).state = false;
                // Force a refresh using IP-based location
                ref.refresh(weatherRefreshProvider.notifier).state++;
              },
            ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: context.containerBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          // border: Border.all(
          //   color: context.secondaryAccentColor.withOpacity(0.3), // 0.3 opacity
          //   width: 1,
          // ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: context.accentColor.withOpacity(0.1),
              ),
              child: Icon(icon, color: context.accentColor),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: context.primaryTextColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.accentColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardAIUsageBar(BuildContext context, WidgetRef ref,
      {double width = 60}) {
    final usageAsync = ref.watch(aiUsageProvider);
    final analyticsService = ref.watch(analyticsServiceProvider);
    final subscriptionAsync = ref.watch(subscriptionProvider);

    // Only show AI usage bar for users with active subscriptions or trials
    final hasActiveSubscription = subscriptionAsync.hasActiveSubscription;
    final subscription = subscriptionAsync.subscription;
    final isInTrialPeriod = subscription?.isInTrialPeriod() == true;

    // Don't show the bar if user doesn't have active subscription or trial
    if (!hasActiveSubscription && !isInTrialPeriod) {
      return const SizedBox.shrink();
    }

    return usageAsync.when(
      data: (usage) {
        final isTrialOrPremium = subscription?.isTrial == true ||
            subscription?.tier == SubscriptionTier.premium;

        // Use remote config values based on subscription status
        final int voiceCap = isTrialOrPremium
            ? analyticsService.getRemoteConfigValue<int>(
                'ai_cap_premium_voice', 75)
            : analyticsService.getRemoteConfigValue<int>(
                'ai_cap_free_voice', 20);
        final int chatCap = isTrialOrPremium
            ? analyticsService.getRemoteConfigValue<int>(
                'ai_cap_premium_chat', 150)
            : analyticsService.getRemoteConfigValue<int>(
                'ai_cap_free_chat', 40);

        final int used = usage.voiceCount + usage.chatCount;
        final int cap = voiceCap + chatCap;
        final double pct = cap == 0 ? 0 : used / cap;

        // Determine bar color from green -> red
        Color barColor =
            Color.lerp(Colors.green, Colors.red, pct) ?? Colors.green;

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              width: 14,
              child: Text(
                'AI',
                style: TextStyle(fontSize: 10),
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: width,
              height: 6,
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(3),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: LinearProgressIndicator(
                  value: pct,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(barColor),
                ),
              ),
            ),
            const SizedBox(width: 4),
            SizedBox(
              width: 38,
              child: Text(
                '${used}/${cap}',
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: context.secondaryTextColor,
                  fontSize: 10,
                  fontFamily: 'RobotoMono',
                ),
              ),
            ),
          ],
        );
      },
      loading: () => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 14,
            child: Text(
              'AI',
              style: TextStyle(fontSize: 10),
            ),
          ),
          const SizedBox(width: 4),
          Container(
            width: width,
            height: 6,
            decoration: BoxDecoration(
              color: context.accentColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(3),
            ),
            child: const LinearProgressIndicator(minHeight: 6),
          ),
          const SizedBox(width: 4),
          const SizedBox(
            width: 38,
            child: Text(
              '--/--',
              textAlign: TextAlign.right,
              style: TextStyle(fontSize: 10),
            ),
          ),
        ],
      ),
      error: (error, stackTrace) => const SizedBox.shrink(),
    );
  }

  Widget _buildNoCarsView(BuildContext context) {
    final l10n = S.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Main welcome card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            color: context.cardColor,
            surfaceTintColor: Colors.transparent,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    context.accentColor.withOpacity(0.05),
                    context.secondaryAccentColor.withOpacity(0.02),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Animated car icon
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: context.accentColor.withOpacity(0.2),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.directions_car,
                      size: 60,
                      color: context.accentColor,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Welcome title
                  Text(
                    l10n.welcomeText,
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      height: 1.2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // Subtitle
                  Text(
                    l10n.dashboardWelcomeSubtitle,
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 16,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // Add car button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => context.push('/cars/add'),
                      icon: const Icon(Icons.add_circle_outline, size: 24),
                      label: Text(
                        l10n.addCar,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Features preview cards
          _buildFeaturePreviewCards(context, l10n),
        ],
      ),
    );
  }

  Widget _buildFeaturePreviewCards(BuildContext context, S l10n) {
    final features = [
      {
        'icon': Icons.schedule,
        'title': l10n.smartReminders,
        'description': l10n.smartRemindersDesc,
        'color': Colors.blue,
      },
      {
        'icon': Icons.analytics,
        'title': l10n.trackHistory,
        'description': l10n.trackHistoryDesc,
        'color': Colors.green,
      },
      {
        'icon': Icons.speed,
        'title': l10n.mileageTracking,
        'description': l10n.mileageTrackingDesc,
        'color': Colors.orange,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Text(
            "What you'll get:",
            style: TextStyle(
              color: context.accentColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Card(
            elevation: 1,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            color: context.cardColor,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: (feature['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      feature['icon'] as IconData,
                      color: feature['color'] as Color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: TextStyle(
                            color: context.primaryTextColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          feature['description'] as String,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 14,
                            height: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        )).toList(),
      ],
    );
  }

  void _showCarSelectionForMaintenanceHistory(BuildContext context) {
    // Safety checks first
    if (widget.cars.isEmpty) {
      // Show message that no cars are available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).addCarFirst),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Count valid cars (with IDs)
    final validCars = widget.cars.where((car) => car.id != null).toList();
    if (validCars.isEmpty) {
      // No cars with valid IDs
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('${S.of(context).errorOccurred}. No cars with valid IDs.'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    if (validCars.length > 1) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select a car to view maintenance history',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                ),
              ),
              const Divider(),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: validCars.length,
                  itemBuilder: (context, index) {
                    final car = validCars[index];
                    return ListTile(
                      leading: Icon(Icons.directions_car,
                          color: context.accentColor),
                      title: Text(
                        '${car.make} ${car.model}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                      ),
                      subtitle: Text(
                        '${car.year}',
                        style: TextStyle(
                          color: context.secondaryTextColor,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        // Safety check that car.id is not null before using it
                        if (car.id != null) {
                          context.push('/cars/${car.id}/maintenance');
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(S.of(context).errorOccurred),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    } else if (validCars.isNotEmpty) {
      // If only one car, navigate directly
      final car = validCars.first;
      if (car.id != null) {
        context.push('/cars/${car.id}/maintenance');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).errorOccurred),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showCarSelectionForMaintenance(BuildContext context) {
    // Safety checks first
    if (widget.cars.isEmpty) {
      // Show message that no cars are available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).addCarFirst),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Count valid cars (with IDs)
    final validCars = widget.cars.where((car) => car.id != null).toList();
    if (validCars.isEmpty) {
      // No cars with valid IDs
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('${S.of(context).errorOccurred}. No cars with valid IDs.'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    if (validCars.length > 1) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select a car for maintenance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                ),
              ),
              const Divider(),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: validCars.length,
                  itemBuilder: (context, index) {
                    final car = validCars[index];
                    return ListTile(
                      leading: Icon(Icons.directions_car,
                          color: context.accentColor),
                      title: Text(
                        '${car.make} ${car.model}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                      ),
                      subtitle: Text(
                        '${car.year}',
                        style: TextStyle(
                          color: context.secondaryTextColor,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        // Safety check that car.id is not null before using it
                        if (car.id != null) {
                          context.push('/maintenance/add?carId=${car.id}');
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(S.of(context).errorOccurred),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    } else if (validCars.isNotEmpty) {
      // If only one car, navigate directly
      final car = validCars.first;
      if (car.id != null) {
        context.push('/maintenance/add?carId=${car.id}');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).errorOccurred),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
