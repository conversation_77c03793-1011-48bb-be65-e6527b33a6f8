import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/providers/locale_provider.dart';
import '../../../../core/providers/auth_providers.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../shared/services/image_cache_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/providers/theme_provider.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final locale = ref.watch(localeProvider);
    final authState = ref.watch(authStateProvider);
    final currentUser = ref.watch(currentUserProvider);
    final imageCacheService = ref.read(imageCacheServiceProvider);
    
    // Get the version from build.gradle
    const appVersion = '1.0.1';

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.settings, 
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: authState == AuthState.authenticated && currentUser != null 
          ? _buildSettingsContent(
              context, 
              ref, 
              l10n, 
              locale, 
              currentUser, 
              imageCacheService,
              appVersion,
            )
          : authState == AuthState.unauthenticated
              ? _buildNotAuthenticatedContent(context, l10n)
              : Center(
                  child: CircularProgressIndicator(color: context.accentColor),
                ),
    );
  }

  Widget _buildSettingsContent(
    BuildContext context,
    WidgetRef ref,
    S l10n,
    Locale locale,
    User currentUser,
    ImageCacheService imageCacheService,
    String appVersion,
  ) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // App Preferences
        _SettingsSection(
          title: l10n.appSettings,
          children: [
            _SettingTile(
              icon: Icons.language,
              title: l10n.language,
              subtitle: locale.languageCode == 'en' ? 'English' : 'العربية',
              onTap: () => ref.read(localeProvider.notifier).toggleLocale(),
            ),
            _SettingTile(
              icon: context.isDarkMode ? Icons.dark_mode : Icons.light_mode,
              title: l10n.themeSettings,
              subtitle: context.isDarkMode ? l10n.darkTheme : l10n.lightTheme,
              onTap: () => _showThemeSelectionDialog(context, ref, l10n),
            ),
            _SettingTile(
              icon: Icons.notifications_outlined,
              title: l10n.notifications,
              subtitle: l10n.notificationSettings,
              onTap: () => context.push('/profile/notifications'),
            ),
            _SettingTile(
              icon: Icons.security,
              title: l10n.permissions,
              subtitle: l10n.manageAppPermissions,
              onTap: () => _showPermissionsDialog(context, l10n),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Legal & Information
        _SettingsSection(
          title: l10n.about,
          children: [
            _SettingTile(
              icon: Icons.info_outline,
              title: l10n.aboutApp,
              subtitle: '${l10n.version} $appVersion',
              onTap: () => _showAboutDialog(context, l10n, appVersion),
            ),
            _SettingTile(
              icon: Icons.privacy_tip_outlined,
              title: l10n.privacyPolicy,
              subtitle: l10n.viewPrivacyPolicy,
              onTap: () => _launchURL(context,
                  'https://vilartech.com/oil_plus/privacy_policy.html'),
            ),
            _SettingTile(
              icon: Icons.gavel_outlined,
              title: l10n.termsAndConditions,
              subtitle: l10n.viewTermsAndConditions,
              onTap: () => _launchURL(context,
                  'https://vilartech.com/oil_plus/terms_and_conditions.html'),
            ),
            _SettingTile(
              icon: Icons.payments_outlined,
              title: l10n.subscriptionPolicy,
              subtitle: l10n.viewSubscriptionPolicy,
              onTap: () => context.push('/subscription-policy'),
            ),
            _SettingTile(
              icon: Icons.request_page_outlined,
              title: l10n.refundPolicy,
              subtitle: l10n.viewRefundPolicy,
              onTap: () => context.push('/refund-policy'),
            ),
          ],
        ),

        const SizedBox(height: 32),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 0),
          child: ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              backgroundColor: context.secondaryAccentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              minimumSize: const Size(double.infinity, 50),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  backgroundColor: context.containerBackgroundColor,
                  title: Text(
                    l10n.signOut,
                    style: TextStyle(color: context.primaryTextColor),
                  ),
                  content: Text(
                    l10n.signOutConfirmation,
                    style: TextStyle(color: context.secondaryTextColor),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        l10n.cancel,
                        style: TextStyle(color: context.accentColor),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        ref.read(authServiceProvider).signOut();
                        context.go('/auth');
                      },
                      child: Text(
                        l10n.signOut,
                        style: TextStyle(color: context.secondaryAccentColor),
                      ),
                    ),
                  ],
                ),
              );
            },
            icon: const Icon(Icons.logout, size: 20),
            label: Text(
              l10n.signOut,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildNotAuthenticatedContent(BuildContext context, S l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle,
            size: 80,
            color: context.accentColor,
          ),
          const SizedBox(height: 24),
          Text(
            l10n.pleaseSignIn,
            style: TextStyle(
              fontSize: 18,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.go('/login'),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.accentColor,
              foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(l10n.login),
          ),
        ],
      ),
    );
  }

  void _showPermissionsDialog(BuildContext context, S l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.permissions,
          style: TextStyle(color: context.accentColor),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _PermissionItem(
                icon: Icons.notifications,
                title: l10n.notifications,
                description: l10n.notificationsPermissionDesc,
              ),
              _PermissionItem(
                icon: Icons.location_on,
                title: l10n.location,
                description: l10n.locationPermissionDesc,
              ),
              _PermissionItem(
                icon: Icons.photo_library,
                title: l10n.storage,
                description: l10n.storagePermissionDesc,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.close,
              style: TextStyle(color: context.accentColor),
            ),
          ),
          TextButton(
            onPressed: () => openAppSettings(),
            child: Text(
              l10n.openSettings,
              style: TextStyle(color: context.accentColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context, S l10n, String appVersion) {
    final textTheme = Theme.of(context).textTheme;
    
    showDialog(
      context: context,
      builder: (context) => Theme(
        data: Theme.of(context).copyWith(
          dialogTheme:
              DialogTheme(backgroundColor: context.containerBackgroundColor),
        ),
        child: Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // App logo and version info
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: context.accentColor),
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/app_icon.png',
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // App name and version
                  Text(
                    l10n.appName,
                    style: TextStyle(
                      color: context.accentColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                    ),
                  ),
                  Text(
                    '${l10n.version} $appVersion',
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 14,
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Features list in scrollable container
 Expanded(
   child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Main features section
                          _buildFeatureSection(
                            context,
                            title: 'Key Features',
                            icon: Icons.star,
                            features: [
                              _FeatureItem(
                                icon: Icons.directions_car,
                                title: l10n.myCars,
                                  description:
                                      'Add and manage multiple vehicles with detailed information'),
                              _FeatureItem(
                                icon: Icons.oil_barrel,
                                title: l10n.appName,
                                  description:
                                      'Track oil change history and receive notifications for upcoming maintenance'),
                              _FeatureItem(
                                icon: Icons.build,
                                title: l10n.maintenance,
                                  description:
                                      'Record all maintenance activities with costs and details'),
                              _FeatureItem(
                                icon: Icons.photo_library,
                                title: l10n.photos,
                                  description:
                                      'Save photos of receipts and maintenance documents'),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Additional features section
                          _buildFeatureSection(
                            context,
                            title: 'Additional Features',
                            icon: Icons.add_circle,
                            features: [
                              _FeatureItem(
                                icon: Icons.badge,
                                title: l10n.driverLicense,
                                  description:
                                      'Track license expiry dates and receive reminders'),
                              _FeatureItem(
                                icon: Icons.cloud,
                                title: 'Weather',
                                  description:
                                      'View current weather conditions for your location'),
                              _FeatureItem(
                                icon: Icons.notifications,
                                title: l10n.notifications,
                                  description:
                                      'Customizable alerts for maintenance and license expiry'),
                              _FeatureItem(
                                icon: Icons.language,
                                title: l10n.language,
                                  description:
                                      'Available in English and Arabic'),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // User account features section
                          _buildFeatureSection(
                            context,
                            title: 'User Account',
                            icon: Icons.person,
                            features: [
                              _FeatureItem(
                                icon: Icons.security,
                                title: l10n.security,
                                  description:
                                      'Secure login with email or Google account'),
                              _FeatureItem(
                                icon: Icons.cloud_upload,
                                title: 'Cloud Backup', 
                                  description:
                                      'All data securely stored in the cloud'),
                              _FeatureItem(
                                icon: Icons.devices,
                                title: 'Multi-Device Access',
                                  description:
                                      'Access your data from multiple devices'),
                              _FeatureItem(
                                icon: Icons.dark_mode,
                                title: l10n.themeSettings,
                                  description:
                                      'Choose between light and dark themes'),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Footer
                  Text(
                    '© ${DateTime.now().year} Maximum Media',
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Close button
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text(l10n.close),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  // Helper method to build a feature section
  Widget _buildFeatureSection(BuildContext context,
    {required String title, 
    required IconData icon,
      required List<_FeatureItem> features}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: context.accentColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.accentColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...features
            .map((feature) => _buildFeatureItem(context, feature))
            .toList(),
      ],
    );
  }
  
  // Helper method to build a feature item
  Widget _buildFeatureItem(BuildContext context, _FeatureItem feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            margin: const EdgeInsets.only(right: 12, top: 2),
            decoration: BoxDecoration(
              color: context.accentColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              feature.icon,
              size: 14,
              color: context.accentColor,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                    fontSize: 14,
                  ),
                ),
                Text(
                  feature.description,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(BuildContext context, String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      print('Could not launch $urlString');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open link: $urlString')),
        );
      }
    }
  }

  void _showThemeSelectionDialog(
    BuildContext context, 
    WidgetRef ref, 
    S l10n,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.themeSettings,
          style: TextStyle(
            color: context.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(
                Icons.light_mode,
                color: context.accentColor,
              ),
              title: Text(
                l10n.lightTheme,
                style: TextStyle(
                  color: context.primaryTextColor,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                ref
                    .read(themeModeProvider.notifier)
                    .setThemeMode(AppThemeMode.light);
              },
              selected: !context.isDarkMode,
            ),
            ListTile(
              leading: Icon(
                Icons.dark_mode,
                color: context.secondaryAccentColor,
              ),
              title: Text(
                l10n.darkTheme,
                style: TextStyle(
                  color: context.primaryTextColor,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                ref
                    .read(themeModeProvider.notifier)
                    .setThemeMode(AppThemeMode.dark);
              },
              selected: context.isDarkMode,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              l10n.cancel,
              style: TextStyle(
                color: context.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Settings section widget
class _SettingsSection extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const _SettingsSection({
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              color: context.accentColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            border: Border.all(color: context.borderColor),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }
}

// Setting tile widget
class _SettingTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _SettingTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon, color: context.accentColor),
      title: Text(
        title,
        style: TextStyle(
          color: context.primaryTextColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: context.secondaryTextColor,
        ),
      ),
      trailing: Icon(Icons.chevron_right, color: context.secondaryAccentColor),
      onTap: onTap,
    );
  }
}

// Permission item widget
class _PermissionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _PermissionItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: context.accentColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Helper class for feature items
class _FeatureItem {
  final IconData icon;
  final String title;
  final String description;
  
  _FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
  });
} 
