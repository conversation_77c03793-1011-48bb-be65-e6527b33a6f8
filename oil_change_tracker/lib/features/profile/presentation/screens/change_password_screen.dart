import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../generated/app_localizations.dart';
import '../../../auth/providers/auth_provider.dart' hide authServiceProvider;
import 'dart:developer' as dev;

class ChangePasswordScreen extends ConsumerStatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  ConsumerState<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _updatePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Add debug logs to track execution
      dev.log('Starting password update process');
      
      // Get current timestamp to compare with after update
      final firebaseAuth = FirebaseAuth.instance;
      final user = firebaseAuth.currentUser;
      
      // Check for null user or email early
      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'No authenticated user found',
        );
      }
      
      if (user.email == null) {
        throw FirebaseAuthException(
          code: 'no-email',
          message: 'Your account has no email address. Cannot change password.',
        );
      }
      
      final initialPasswordUpdatedAt = user.metadata.lastSignInTime;
      dev.log('Initial password timestamp: ${initialPasswordUpdatedAt?.toIso8601String()}');
      
      // Attempt password update
      await ref.read(authProvider.notifier).updatePassword(
            currentPassword: _currentPasswordController.text,
            newPassword: _newPasswordController.text,
          );

      dev.log('Password update API call completed');
      if (!mounted) return;
      
      // Verify password was actually changed by reloading user and checking metadata
      try {
        // Force reload the user to get updated metadata
        final currentUser = firebaseAuth.currentUser;
        if (currentUser != null) {
          dev.log('Verifying password change success');
          // Reload user to get fresh metadata
          await currentUser.reload();
          final updatedUser = firebaseAuth.currentUser;
          final updatedTimestamp = updatedUser?.metadata.lastSignInTime;
          dev.log('Updated password timestamp: ${updatedTimestamp?.toIso8601String()}');
          
          // If user has been freshly authenticated (due to reauthentication in the update process)
          // or metadata has changed, we consider it a success
          if (initialPasswordUpdatedAt != updatedTimestamp) {
            dev.log('Password change verified successful - metadata changed');
            _showSuccessAndNavigateBack();
            return;
          }
          
          // Additional verification: attempt to sign in with new password if needed
          dev.log('No timestamp change detected, performing additional verification');
          try {
            // Check for null email before creating credential
            if (updatedUser!.email == null) {
              throw FirebaseAuthException(
                code: 'no-email',
                message: 'Current user has no email address – cannot re-authenticate.',
              );
            }
            
            // Try to create a credential with the new password
            final credential = EmailAuthProvider.credential(
              email: updatedUser.email!,
              password: _newPasswordController.text,
            );
            
            // Attempt to reauthenticate with new password
            await updatedUser.reauthenticateWithCredential(credential);
            dev.log('Reauthentication with new password succeeded - password change verified');
            _showSuccessAndNavigateBack();
            return;
          } catch (verifyError) {
            dev.log('Reauthentication verification failed: $verifyError');
            // If this fails, the password was not actually changed
            throw Exception('Password update verification failed: $verifyError');
          }
        } else {
          throw Exception('User is null after password update');
        }
      } catch (verifyError) {
        dev.log('Password change verification error: $verifyError');
        throw Exception('Could not verify password change: $verifyError');
      }
    } catch (e) {
      dev.log('Password change error: $e');
      
      if (!mounted) return;
      
      // Use typed exception checking instead of string matching
      String errorMsg = S.of(context).errorOccurred;
      
      if (e is FirebaseAuthException) {
        // Handle specific Firebase Auth error codes
        switch (e.code) {
          case 'wrong-password':
            errorMsg = 'Current password is incorrect';
            break;
          case 'weak-password':
            errorMsg = 'New password is too weak';
            break;
          case 'no-email':
            errorMsg = 'Your account has no email address. Cannot change password.';
            break;
          case 'network-request-failed':
            errorMsg = 'Network error. Please check your connection';
            break;
          case 'app-not-authorized':
          case 'internal-error':
            // Check for PigeonUserDetails in internal errors
            if (e.message?.contains('PigeonUserDetails') == true ||
                e.message?.contains('List<Object?>') == true) {
              // Try to verify through current user reload
              try {
                final currentUser = FirebaseAuth.instance.currentUser;
                if (currentUser != null) {
                  await currentUser.reload();
                  dev.log('User reload successful after platform error - assuming password change succeeded');
                  _showSuccessAndNavigateBack();
                  return;
                }
              } catch (reloadError) {
                dev.log('User reload failed: $reloadError');
                // Fall through to the error message
              }
            }
            break;
        }
      }
      
      // Show error dialog
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            title: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 30),
                const SizedBox(width: 10),
                Text(S.of(context).error),
              ],
            ),
            content: Text(
              errorMsg,
              style: const TextStyle(fontSize: 16),
            ),
            actions: [
              TextButton(
                child: Text(S.of(context).ok),
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
              ),
            ],
          );
        },
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
  
  // Helper method to show success message and navigate back
  void _showSuccessAndNavigateBack() {
    // Reset the form
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();
    
    // Show synchronous success dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          title: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 30),
              const SizedBox(width: 10),
              Text(S.of(context).success),
            ],
          ),
          content: Text(
            S.of(context).passwordUpdatedSuccessfully,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              child: Text(S.of(context).ok),
              onPressed: () {
                // First close the dialog
                Navigator.of(dialogContext).pop();
                // Then navigate back from the screen
                context.pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.changePassword,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.2),
            ],
            stops: const [0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: screenHeight - AppBar().preferredSize.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.lock_outline,
                        size: 80,
                        color: context.accentColor,
                      ),
                      const SizedBox(height: 32),
                      Text(
                        l10n.changePassword,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      _buildPasswordField(
                        controller: _currentPasswordController,
                        label: l10n.currentPassword,
                        obscureText: _obscureCurrentPassword,
                        onToggleVisibility: () {
                          setState(() => _obscureCurrentPassword = !_obscureCurrentPassword);
                        },
                        validator: FormValidators.validatePassword,
                      ),
                      const SizedBox(height: 20),
                      _buildPasswordField(
                        controller: _newPasswordController,
                        label: l10n.newPassword,
                        obscureText: _obscureNewPassword,
                        onToggleVisibility: () {
                          setState(() => _obscureNewPassword = !_obscureNewPassword);
                        },
                        validator: FormValidators.validateNewPassword,
                      ),
                      const SizedBox(height: 20),
                      _buildPasswordField(
                        controller: _confirmPasswordController,
                        label: l10n.confirmNewPassword,
                        obscureText: _obscureConfirmPassword,
                        onToggleVisibility: () {
                          setState(() => _obscureConfirmPassword = !_obscureConfirmPassword);
                        },
                        validator: (value) => FormValidators.validateConfirmPassword(
                          value,
                          _newPasswordController.text,
                        ),
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _updatePassword,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.accentColor,
                          foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          minimumSize: const Size(double.infinity, 56),
                          elevation: 4,
                        ),
                        child: _isLoading
                            ? SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(context.isDarkMode ? Colors.black : Colors.white),
                                ),
                              )
                            : Text(
                                l10n.updatePassword,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      style: TextStyle(color: context.primaryTextColor),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: context.secondaryTextColor),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: context.secondaryAccentColor.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: context.accentColor),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility : Icons.visibility_off,
            color: context.secondaryTextColor,
          ),
          onPressed: onToggleVisibility,
        ),
      ),
      validator: validator,
    );
  }
} 