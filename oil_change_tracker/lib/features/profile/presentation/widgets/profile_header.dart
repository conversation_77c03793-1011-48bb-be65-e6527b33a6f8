import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/services/image_cache_service.dart';
import '../../../subscription/providers/subscription_provider.dart';
import '../../../../core/models/user_model.dart';

class ProfileHeader extends ConsumerWidget {
  const ProfileHeader({
    super.key,
    required this.user,
    required this.photoUrl,
    required this.onEditPhoto,
    required this.isUpdatingImage,
  });

  final UserModel user;
  final String? photoUrl;
  final VoidCallback onEditPhoto;
  final bool isUpdatingImage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final forceRefreshTimestamp = photoUrl.hashCode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 28),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            context.accentColor.withOpacity(0.6),
            context.secondaryAccentColor.withOpacity(0.3),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 4),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    CircleAvatar(
                      radius: 42,
                      backgroundColor: Colors.white.withOpacity(0.8),
                    ),
                    isUpdatingImage
                        ? const SizedBox(
                            height: 28,
                            width: 28,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Hero(
                            tag:
                                'profile-image-${user.id}-$forceRefreshTimestamp',
                            child: ClipOval(
                              child: ref
                                  .watch(imageCacheServiceProvider)
                                  .buildProfileImage(
                                    userId: user.id ?? '',
                                    imageUrl: photoUrl,
                                    size: 90,
                                    backgroundColor:
                                        Colors.white.withOpacity(0.8),
                                    foregroundColor: context.accentColor,
                                    forceRefresh: true,
                                  ),
                            ),
                          ),
                  ],
                ),
              ),
              Consumer(builder: (context, ref, _) {
                final sub = ref.watch(subscriptionProvider);
                if (sub.hasActiveSubscription) {
                  return Positioned(
                    top: 6,
                    right: 6,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(Icons.workspace_premium,
                          color: AppColors.gold, size: 20),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: onEditPhoto,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: context.accentColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    child:
                        const Icon(Icons.edit, color: Colors.white, size: 16),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            user.displayName,
            style: const TextStyle(
                fontSize: 18, fontWeight: FontWeight.w600, color: Colors.white),
          ),
        ],
      ),
    );
  }
}
