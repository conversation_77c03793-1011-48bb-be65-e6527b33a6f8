import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../ai_usage/providers/ai_usage_providers.dart';
import '../../../subscription/providers/subscription_provider.dart';
import '../../../subscription/models/subscription_tier.dart';
import '../../../../generated/app_localizations.dart';

class ProfileStats extends ConsumerWidget {
  const ProfileStats({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final usageAsync = ref.watch(aiUsageProvider);
    final subscription = ref.watch(subscriptionProvider);

    final hasAccess = subscription.hasActiveSubscription ||
        (subscription.subscription?.isInTrialPeriod() ?? false);

    if (!hasAccess) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      child: Column(
        children: [
          _buildUsageBar(context, usageAsync, l10n, ref),
          const SizedBox(height: 6),
          _buildTrialCountdown(context, subscription, l10n),
        ],
      ),
    );
  }

  Widget _buildUsageBar(
      BuildContext context, AsyncValue usageAsync, S l10n, WidgetRef ref) {
    return usageAsync.when(
      data: (usage) {
        final tier = ref.read(subscriptionProvider).subscription?.tier ??
            SubscriptionTier.free;
        final limit = tier.getCapVoice() + tier.getCapChat();
        final used = usage.voiceCount + usage.chatCount;
        final percent = limit == 0 ? 0.0 : used / limit;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.aiFeatures,
                style: TextStyle(color: context.primaryTextColor)),
            const SizedBox(height: 4),
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: LinearProgressIndicator(
                value: percent.clamp(0, 1),
                minHeight: 8,
                backgroundColor: context.secondaryAccentColor.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation(context.accentColor),
              ),
            ),
            const SizedBox(height: 4),
            Text('$used/$limit'),
          ],
        );
      },
      loading: () => const LinearProgressIndicator(minHeight: 8),
      error: (e, _) => const SizedBox.shrink(),
    );
  }

  Widget _buildTrialCountdown(BuildContext context, subscriptionState, S l10n) {
    final sub = subscriptionState.subscription;
    if (sub == null || !sub.isTrial) return const SizedBox.shrink();
    final daysLeft = sub.expiryDate!.difference(DateTime.now()).inDays;
    return Text(l10n.expiresInDays(daysLeft),
        style: TextStyle(color: context.secondaryTextColor, fontSize: 12));
  }
}
