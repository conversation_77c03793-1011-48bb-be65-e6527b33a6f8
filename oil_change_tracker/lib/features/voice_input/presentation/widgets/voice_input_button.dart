import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';
import '../../../../features/subscription/presentation/widgets/premium_features_list.dart';
import '../../../../generated/app_localizations.dart';
import '../providers/voice_recording_provider.dart';
import '../../models/voice_form_type.dart';

/// A button that allows the user to input data via voice
class VoiceInputButton extends ConsumerStatefulWidget {
  /// Function to call when transcription is complete
  final Function(String) onTranscriptionComplete;

  /// Language code for speech recognition
  final String languageCode;

  /// Type of form this voice input is for
  final VoiceFormType formType;

  /// Create a new voice input button
  const VoiceInputButton({
    super.key,
    required this.onTranscriptionComplete,
    this.languageCode = 'en-US',
    required this.formType,
  });

  @override
  ConsumerState<VoiceInputButton> createState() => _VoiceInputButtonState();
}

class _VoiceInputButtonState extends ConsumerState<VoiceInputButton> {
  bool _isRecording = false;

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: _handleVoiceInput,
      backgroundColor:
          _isRecording ? Colors.red : Theme.of(context).colorScheme.primary,
      child: Icon(
        _isRecording ? Icons.stop : Icons.mic,
        color: Colors.white,
      ),
    );
  }

  Future<void> _handleVoiceInput() async {
    // Check if the user has access to voice input feature
    final hasVoiceAccess =
        ref.read(featureGateProvider(PremiumFeature.voiceInput));

    if (!hasVoiceAccess) {
      // Show subscription promotion dialog
      _showSubscriptionPromotion();
      return;
    }

    final voiceNotifier = ref.read(voiceRecordingProvider.notifier);

    if (_isRecording) {
      setState(() {
        _isRecording = false;
      });

      await voiceNotifier.stopRecording();
      final state = ref.read(voiceRecordingProvider);

      if (state.transcribedText != null && state.transcribedText!.isNotEmpty) {
        widget.onTranscriptionComplete(state.transcribedText!);
      }
    } else {
      setState(() {
        _isRecording = true;
      });

      try {
        await voiceNotifier.startRecording(
          languageCode: widget.languageCode,
          formType: widget.formType == VoiceFormType.oilChange
              ? 'oil_change'
              : 'maintenance',
        );
      } catch (e) {
        print('Error starting recording: $e');
        setState(() {
          _isRecording = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to start recording: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  // Show subscription promotion dialog
  void _showSubscriptionPromotion() {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.premiumRequired),
            const SizedBox(height: 16),
            Text(l10n.upgradeToRemoveAds),
            const SizedBox(height: 12),
            const SimplePremiumFeaturesList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }
}
