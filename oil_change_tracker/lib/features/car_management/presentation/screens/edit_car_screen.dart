import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/car_model.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../providers/car_provider.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../profile/data/services/image_service.dart';
import '../../../../shared/widgets/image_carousel.dart';
import 'dart:developer' as dev;
import '../../../../core/providers/auth_providers.dart';
// Removed duplicate import
import '../../../../core/services/auth_service.dart';
// Notification service is used through the provider

class EditCarScreen extends ConsumerStatefulWidget {
  final String carId;

  const EditCarScreen({
    super.key,
    required this.carId,
  });

  @override
  ConsumerState<EditCarScreen> createState() => _EditCarScreenState();
}

class _EditCarScreenState extends ConsumerState<EditCarScreen> {
  final _formKey = GlobalKey<FormState>();
  final _makeController = TextEditingController();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _currentMileageController = TextEditingController();
  final _lastOilChangeMileageController = TextEditingController();
  final _monthIntervalController = TextEditingController();
  CarModel? _car;
  bool _isLoading = false;
  List<String> _networkImages = [];
  final List<File> _localImages = [];
  late final ImageService _imageService;
  int _oilChangeIntervalValue = 5000;
  int _oilChangeMonthInterval = 6;
  DateTime _lastOilChangeDate = DateTime.now();
  DateTime? _licenseExpiryDate; // Added for license expiry

  @override
  void initState() {
    super.initState();
    _imageService = ref.read(imageServiceProvider);
    _initializeCarData();
  }

  Future<void> _initializeCarData() async {
    final carRepository = ref.read(carRepositoryProvider);
    try {
      final car = await carRepository.getCar(widget.carId);
      if (mounted) {
        setState(() {
          _car = car;
          _makeController.text = car.make;
          _modelController.text = car.model;
          _yearController.text = car.year.toString();
          _currentMileageController.text = car.currentMileage.toString();
          _lastOilChangeMileageController.text = car.lastOilChangeMileage.toString();
          _oilChangeIntervalValue = car.oilChangeMileageInterval;
          _oilChangeMonthInterval = car.oilChangeMonthInterval;
          _monthIntervalController.text = car.oilChangeMonthInterval.toString();
          _lastOilChangeDate = car.lastOilChangeDate;
          _licenseExpiryDate = car.licenseExpiryDate; // Initialize license expiry date
          if (car.imageUrls != null && car.imageUrls!.isNotEmpty) {
            _networkImages = List.from(car.imageUrls!);
          }
        });
      }
    } catch (e) {
      dev.log('Error loading car data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).error}: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _makeController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _currentMileageController.dispose();
    _lastOilChangeMileageController.dispose();
    _monthIntervalController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: context.containerBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: context.accentColor.withOpacity(0.2)))
            ),
            child: Row(
              children: [
                Icon(Icons.photo_camera, color: context.accentColor),
                const SizedBox(width: 8),
                Text(
                  S.of(context).chooseImageSource,
                  style: TextStyle(
                    color: context.accentColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: Icon(Icons.photo_library, color: context.accentColor),
            title: Text(S.of(context).gallery, style: TextStyle(color: context.primaryTextColor)),
            onTap: () {
              Navigator.pop(context);
              _getImage(ImageSource.gallery);
            },
          ),
          if (_localImages.isNotEmpty || _networkImages.isNotEmpty)
            ListTile(
              leading: Icon(Icons.delete, color: context.secondaryAccentColor),
              title: Text(S.of(context).removePhoto, style: TextStyle(color: context.primaryTextColor)),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _localImages.clear();
                  _networkImages.clear();
                });
              },
            ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final List<XFile>? pickedFiles = await _imageService.pickImages(source: source);

      if (pickedFiles != null && pickedFiles.isNotEmpty) {
        setState(() {
          for (var pickedFile in pickedFiles) {
            _localImages.add(File(pickedFile.path));
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${S.of(context).error}: $e'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _car!.lastOilChangeDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _car!.lastOilChangeDate) {
      setState(() {
        // _car = _car!.copyWith(lastOilChangeDate: picked); // This was for the CarModel instance directly
        _lastOilChangeDate = picked; // Update the state variable
      });
    }
  }

  Future<void> _selectLicenseExpiryDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _licenseExpiryDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 5)), // Allow past dates for already expired
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)), // Max 10 years in future
    );
    if (picked != null && picked != _licenseExpiryDate) {
      setState(() {
        _licenseExpiryDate = picked;
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate() || _car == null) return;

    // Set loading state with clear message
    setState(() => _isLoading = true);
    final l10n = S.of(context);

    try {
      final currentMileage = int.parse(_currentMileageController.text);
      final lastOilChangeMileage = int.parse(_lastOilChangeMileageController.text);

      if (lastOilChangeMileage > currentMileage) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.invalidMileage),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
        return;
      }

      // Start with existing network images
      List<String> allImageUrls = List.from(_networkImages);

      // Upload new images if selected
      if (_localImages.isNotEmpty) {
        final authState = ref.read(authStateProvider);
        if (authState != AuthState.authenticated) {
          throw Exception('User not authenticated');
        }

        final user = ref.read(currentUserProvider);
        final userId = user?.uid ?? '';

        if (userId.isEmpty) {
          throw Exception('User ID not available');
        }

        try {
          final uploadedUrls = await _imageService.uploadImages(
            _localImages,
            userId,
            ImageType.carImage,
          );

          if (uploadedUrls.isNotEmpty) {
            allImageUrls.addAll(uploadedUrls);
          } else {
            // If upload failed but we should continue with car update
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(S.of(context).carSavedAnyway),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } catch (e) {
          // Log error but continue with car update
          dev.log('Error uploading images: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${l10n.error}: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }

      // Create updated car model
      final updatedCar = _car!.copyWith(
        make: _makeController.text.trim(),
        model: _modelController.text.trim(),
        year: int.parse(_yearController.text),
        currentMileage: currentMileage,
        lastOilChangeMileage: lastOilChangeMileage,
        lastOilChangeDate: _lastOilChangeDate,
        oilChangeMileageInterval: _oilChangeIntervalValue,
        oilChangeMonthInterval: _oilChangeMonthInterval,
        imageUrls: allImageUrls,
        imageUrl: allImageUrls.isNotEmpty ? allImageUrls.first : null,
        licenseExpiryDate: _licenseExpiryDate, // Add license expiry date
      );

      await ref.read(carsNotifierProvider.notifier).updateCar(updatedCar, context);

      // Wait briefly before navigating back to ensure smooth transition
      await Future.delayed(const Duration(milliseconds: 300));

      // Refresh car details screen
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      dev.log('Error updating car: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n.error}: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // We're using carsProvider directly, so we don't need to watch carNotifierProvider
    final carsAsync = ref.watch(carsProvider);
    final l10n = S.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.editCar),
      ),
      body: carsAsync.when(
        data: (cars) {
          // Initialize car data if not already done
          if (_car == null) {
            try {
              final car = cars.firstWhere((car) => car.id == widget.carId);
              _car = car;

              // Populate form fields with car data
              _makeController.text = car.make;
              _modelController.text = car.model;
              _yearController.text = car.year.toString();
              _currentMileageController.text = car.currentMileage.toString();
              _lastOilChangeMileageController.text = car.lastOilChangeMileage.toString();
              _oilChangeIntervalValue = car.oilChangeMileageInterval;
              _oilChangeMonthInterval = car.oilChangeMonthInterval;
              _monthIntervalController.text = car.oilChangeMonthInterval.toString();
              _lastOilChangeDate = car.lastOilChangeDate;
              _licenseExpiryDate = car.licenseExpiryDate; // Initialize license expiry date from car model
              _networkImages = List.from(car.imageUrls ?? []);
            } catch (e) {
              // Show error if car not found
              return Center(
                child: Text('${l10n.errorLoadingCars}: $e'),
              );
            }
          }

          return LoadingOverlay(
            isLoading: _isLoading,
            // Add loading message
            message: _isLoading ? "Updating car..." : null,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Car Image Picker
                    _buildCarImagePicker(l10n),
                    CustomTextField(
                      controller: _makeController,
                      labelText: l10n.make,
                      textCapitalization: TextCapitalization.words,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.pleaseEnterCarMake;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _modelController,
                      labelText: l10n.model,
                      textCapitalization: TextCapitalization.words,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.pleaseEnterCarModel;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _yearController,
                      labelText: l10n.year,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.pleaseEnterCarYear;
                        }
                        final year = int.tryParse(value);
                        if (year == null) {
                          return l10n.pleaseEnterValidYear;
                        }
                        if (year < 1900 || year > DateTime.now().year) {
                          return l10n.yearRangeError;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _currentMileageController,
                      labelText: l10n.currentMileage,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.pleaseEnterMileage;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _lastOilChangeMileageController,
                      labelText: l10n.lastOilChangeMileage,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return l10n.pleaseEnterLastOilChangeMileage;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: Text(l10n.lastOilChange),
                      subtitle: Text(
                        // Use the state variable _lastOilChangeDate directly
                        '${_lastOilChangeDate.year}-${_lastOilChangeDate.month.toString().padLeft(2, '0')}-${_lastOilChangeDate.day.toString().padLeft(2, '0')}',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () => _selectDate(context), // No need to check _car != null if _lastOilChangeDate is initialized
                    ),
                    const SizedBox(height: 16), // Add some spacing
                    // License Expiry Date Picker
                    ListTile(
                      title: Text(l10n.licenseExpiryDate),
                      subtitle: Text(
                        _licenseExpiryDate != null
                            ? '${_licenseExpiryDate!.year}-${_licenseExpiryDate!.month.toString().padLeft(2, '0')}-${_licenseExpiryDate!.day.toString().padLeft(2, '0')}'
                            : l10n.notSet,
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () => _selectLicenseExpiryDate(context),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      l10n.maintenanceIntervals,
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: context.accentColor),
                        borderRadius: BorderRadius.circular(8),
                        color: context.containerBackgroundColor.withOpacity(0.5),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.repeat, color: context.accentColor),
                              const SizedBox(width: 8),
                              Text(
                                l10n.oilChangeInterval,
                                style: TextStyle(
                                  color: context.accentColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            l10n.selectRecommendedInterval,
                            style: TextStyle(
                              color: context.secondaryTextColor,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: context.secondaryAccentColor.withOpacity(0.2),
                              border: Border.all(color: context.secondaryAccentColor),
                            ),
                            child: Text(
                              l10n.selected("$_oilChangeIntervalValue ${l10n.distance}"),
                              style: TextStyle(
                                color: context.primaryTextColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          GridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: 2,
                            childAspectRatio: 2.2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            children: [
                              _buildIntervalRadio(3000, l10n),
                              _buildIntervalRadio(5000, l10n),
                              _buildIntervalRadio(7500, l10n),
                              _buildIntervalRadio(10000, l10n),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Time-based Oil Change Interval
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: context.accentColor),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.black.withOpacity(0.1),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.calendar_today, color: context.accentColor),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  S.of(context).timeBasedOilChangeInterval,
                                  style: TextStyle(
                                    color: context.accentColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              Tooltip(
                                message: S.of(context).timeBasedIntervalExplanation,
                                child: Icon(Icons.info_outline, color: context.accentColor, size: 16),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            S.of(context).timeBasedIntervalDescription,
                            style: TextStyle(
                              color: context.primaryTextColor.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: Slider(
                                  value: _oilChangeMonthInterval.toDouble(),
                                  min: 1,
                                  max: 12,
                                  divisions: 11,
                                  activeColor: context.secondaryAccentColor,
                                  inactiveColor: context.accentColor.withOpacity(0.3),
                                  label: "$_oilChangeMonthInterval ${l10n.months}",
                                  onChanged: (value) {
                                    setState(() {
                                      _oilChangeMonthInterval = value.toInt();
                                      _monthIntervalController.text = value.toInt().toString();
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 1,
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    controller: _monthIntervalController,
                                    keyboardType: TextInputType.number,
                                    textAlign: TextAlign.center,
                                    decoration: InputDecoration(
                                      suffixText: l10n.months.substring(0, 2),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.accentColor.withOpacity(0.5)),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.accentColor),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                                    ),
                                    onChanged: (value) {
                                      final intValue = int.tryParse(value);
                                      if (intValue != null && intValue >= 1 && intValue <= 12) {
                                        setState(() {
                                          _oilChangeMonthInterval = intValue;
                                        });
                                      }
                                    },
                                    validator: _validateMonthInterval,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                _buildMonthChip(3, l10n),
                                const SizedBox(width: 8),
                                _buildMonthChip(6, l10n),
                                const SizedBox(width: 8),
                                _buildMonthChip(9, l10n),
                                const SizedBox(width: 8),
                                _buildMonthChip(12, l10n),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: context.secondaryAccentColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: context.secondaryAccentColor.withOpacity(0.3)),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.notifications_active, color: context.accentColor, size: 16),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    S.of(context).notificationExplanation,
                                    style: TextStyle(
                                      color: context.primaryTextColor.withOpacity(0.7),
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    CustomButton(
                      text: l10n.saveChanges,
                      onPressed: _submitForm,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Text('${l10n.error}: $error'),
        ),
      ),
    );
  }

  Widget _buildCarImagePicker(S l10n) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ImageCarousel(
            height: 250,
            networkImages: _networkImages,
            localImages: _localImages,
            onAddImageTap: _pickImage,
            onDeleteImageTap: _deleteImage,
            enableAdd: true,
            enableDelete: true,
            imageFit: BoxFit.cover,
            borderRadius: BorderRadius.circular(16),
          ),
        ],
      ),
    );
  }

  void _deleteImage(int index) {
    setState(() {
      if (index < _networkImages.length) {
        _networkImages.removeAt(index);
      } else {
        // Adjust index to account for network images
        _localImages.removeAt(index - _networkImages.length);
      }
    });
  }

  Widget _buildIntervalRadio(int interval, S l10n) {
    final bool isSelected = _oilChangeIntervalValue == interval;
    return InkWell(
      onTap: () {
        setState(() => _oilChangeIntervalValue = interval);
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? context.secondaryAccentColor : context.accentColor.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          color: isSelected
            ? context.secondaryAccentColor.withOpacity(0.2)
            : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.scale(
              scale: 1.2,
              child: Radio<int>(
                value: interval,
                groupValue: _oilChangeIntervalValue,
                activeColor: context.secondaryAccentColor,
                fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                  if (states.contains(WidgetState.selected)) {
                    return context.secondaryAccentColor;
                  }
                  return context.accentColor;
                }),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                onChanged: (newValue) {
                  setState(() => _oilChangeIntervalValue = newValue!);
                },
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                "$interval ${l10n.distance}",
                style: TextStyle(
                  color: isSelected ? context.primaryTextColor : context.accentColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 12,
                ),
                overflow: TextOverflow.visible,
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthChip(int months, S l10n) {
    final isSelected = _oilChangeMonthInterval == months;
    return InkWell(
      onTap: () {
        setState(() {
          _oilChangeMonthInterval = months;
          _monthIntervalController.text = months.toString();
        });
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? context.secondaryAccentColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? context.secondaryAccentColor : context.accentColor.withOpacity(0.5),
          ),
        ),
        child: Text(
          "$months ${l10n.months}",
          style: TextStyle(
            color: isSelected ? context.primaryTextColor : context.accentColor,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  String? _validateMonthInterval(String? value) {
    if (value == null || value.isEmpty) {
      return S.of(context).pleaseEnterCarMake;
    }
    final intValue = int.tryParse(value);
    if (intValue == null || intValue < 1 || intValue > 12) {
      return S.of(context).invalidMonthRange;
    }
    return null;
  }
}