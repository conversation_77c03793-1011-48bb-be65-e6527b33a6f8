import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/car_model.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/image_carousel.dart';
import '../providers/car_provider.dart';
import '../../../auth/data/providers/auth_provider.dart';
import '../../../profile/data/services/image_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/notification_service.dart'; // Import NotificationService
import '../../../ads/presentation/managers/interstitial_ad_manager.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/widgets/unified_app_bar.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';

class AddCarScreen extends ConsumerStatefulWidget {
  const AddCarScreen({super.key});

  @override
  ConsumerState<AddCarScreen> createState() => _AddCarScreenState();
}

class _AddCarScreenState extends ConsumerState<AddCarScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _currentMileageController = TextEditingController();
  final _lastOilChangeMileageController = TextEditingController();
  DateTime _lastOilChangeDate = DateTime.now();
  DateTime? _licenseExpiryDate; // Added for license expiry
  double _oilChangeMileageInterval = 5000;
  int _oilChangeMonthInterval = 6;
  final _monthIntervalController = TextEditingController();
  bool _isLoading = false;
  late AnimationController _animationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _pulseAnimation;
  final List<File> _selectedImages = [];
  final List<String> _imageUrls = [];
  late ImageService _imageService;
  final _nameController = TextEditingController();
  final _makeController = TextEditingController();
  bool _isImageSelected = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..forward();

    // Setup pulse animation
    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(
      CurvedAnimation(
        parent: _pulseAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _imageService = ref.read(imageServiceProvider);
    _monthIntervalController.text = _oilChangeMonthInterval.toString();
  }

  @override
  void dispose() {
    _modelController.dispose();
    _yearController.dispose();
    _currentMileageController.dispose();
    _lastOilChangeMileageController.dispose();
    _animationController.dispose();
    _pulseAnimationController.dispose();
    _nameController.dispose();
    _makeController.dispose();
    _monthIntervalController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: context.containerBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        color: context.accentColor.withOpacity(0.2)))),
            child: Row(
              children: [
                Icon(Icons.photo_camera, color: context.accentColor),
                const SizedBox(width: 8),
                Text(
                  S.of(context).chooseImageSource,
                  style: TextStyle(
                    color: context.accentColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: Icon(Icons.photo_library, color: context.accentColor),
            title: Text(S.of(context).gallery,
                style: TextStyle(color: context.primaryTextColor)),
            onTap: () {
              Navigator.pop(context);
              _getImage(ImageSource.gallery);
            },
          ),
          if (_selectedImages.isNotEmpty)
            ListTile(
              leading: Icon(Icons.delete, color: context.secondaryAccentColor),
              title: Text(S.of(context).removePhoto,
                  style: TextStyle(color: context.primaryTextColor)),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedImages.clear();
                  _imageUrls.clear();
                });
              },
            ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final List<XFile>? pickedFiles =
          await _imageService.pickImages(source: source);

      if (pickedFiles != null && pickedFiles.isNotEmpty) {
        setState(() {
          for (var pickedFile in pickedFiles) {
            _selectedImages.add(File(pickedFile.path));
          }
          _isImageSelected = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${S.of(context).errorUploadingImage} - Car will be saved anyway'),
            backgroundColor: context.secondaryAccentColor,
          ),
        );
      }
    }
  }

  Future<void> _saveCar() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Hide keyboard
    FocusScope.of(context).unfocus();

    setState(() => _isLoading = true);

    try {
      // Track save attempt
      final analyticsService = ref.read(analyticsServiceProvider);
      analyticsService.trackFunnelStep(
        funnel: AnalyticsFunnel.carCreation,
        step: 'save_attempt',
        isCompleted: false,
      );

      // Create car model
      final authState = ref.read(authStateChangesProvider).value;
      if (authState == null) {
        throw Exception('User not authenticated');
      }

      final car = CarModel(
        userId: authState.uid,
        make: _makeController.text.trim(),
        model: _modelController.text.trim(),
        year: int.parse(_yearController.text.trim()),
        currentMileage: int.parse(
            _currentMileageController.text.replaceAll(',', '').trim()),
        lastOilChangeDate: _lastOilChangeDate ?? DateTime.now(),
        lastOilChangeMileage: int.parse(
            _lastOilChangeMileageController.text.replaceAll(',', '').trim()),
        oilChangeMileageInterval: _oilChangeMileageInterval.toInt(),
        oilChangeMonthInterval: _oilChangeMonthInterval,
        imageUrls: _imageUrls,
        licenseExpiryDate: _licenseExpiryDate,
      );

      // Save car to Firestore
      await ref.read(carNotifierProvider.notifier).addCar(car);

      // Track analytics
      analyticsService.logCarEvent(
        eventType: AnalyticsEventType.carAdded,
        car: car,
        additionalParams: {
          'mileage_interval': _oilChangeMileageInterval.toInt(),
          'month_interval': _oilChangeMonthInterval,
          'has_images': _imageUrls.isNotEmpty,
          'image_count': _imageUrls.length,
        },
      );

      // Track funnel completion
      analyticsService.trackFunnelStep(
        funnel: AnalyticsFunnel.carCreation,
        step: 'car_saved',
        isCompleted: true,
      );

      // Show Interstitial Ad after successful save
      ref.read(interstitialAdManagerProvider.notifier).showAdIfAvailable();

      // Schedule license expiry notification
      if (car.licenseExpiryDate != null) {
        await ref
            .read(notificationServiceProvider)
            .scheduleLicenseExpiryNotification(car, context);
      }

      // Schedule oil change notification
      await ref
          .read(notificationServiceProvider)
          .scheduleOilChangeNotification(car, context);

      // When success - check if this was the first car and show celebration
      if (mounted) {
        // Check if this was the user's first car
        final carsAsync = ref.read(carsProvider);
        final isFirstCar = carsAsync.when(
          data: (cars) => cars.isEmpty,
          loading: () => false,
          error: (_, __) => false,
        );

        // Wait for a short time to allow state to update before navigating back
        await Future.delayed(const Duration(milliseconds: 300));

        // Manually refresh cars provider to ensure dashboard shows latest data
        ref.invalidate(carsProvider);

        // Show success message for first car
        if (isFirstCar && mounted) {
          _showFirstCarSuccessDialog();
        } else {
          // Navigate back to dashboard for subsequent cars
          if (mounted) {
            context.pop();
          }
        }
      }
    } catch (e) {
      // Check if this is a vehicle limit error
      if (e.toString().contains('limited to 3 vehicles')) {
        // Show subscription promotion dialog
        _showSubscriptionPromotion();
      } else {
        // Track error in car creation
        ref.read(analyticsServiceProvider).trackFunnelStep(
          funnel: AnalyticsFunnel.carCreation,
          step: 'car_creation_error',
          isCompleted: false,
          parameters: {
            'error': e.toString(),
          },
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).errorOccurred),
              backgroundColor: context.secondaryAccentColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Show first car success dialog
  void _showFirstCarSuccessDialog() {
    final l10n = S.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Success animation icon
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.check_circle,
                size: 60,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 24),

            // Success title
            Text(
              l10n.congratulations,
              style: TextStyle(
                color: context.accentColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Success message
            Text(
              l10n.firstCarSuccessMessage,
              style: TextStyle(
                color: context.secondaryTextColor,
                fontSize: 16,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Next steps
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: context.accentColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.whatsNext,
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildNextStepItem(
                    Icons.oil_barrel,
                    l10n.nextStepRecordOil,
                    context,
                  ),
                  const SizedBox(height: 8),
                  _buildNextStepItem(
                    Icons.notifications,
                    l10n.nextStepReminders,
                    context,
                  ),
                  const SizedBox(height: 8),
                  _buildNextStepItem(
                    Icons.analytics,
                    l10n.nextStepTrackHealth,
                    context,
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.pop(); // Navigate back to dashboard
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                l10n.getStarted,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(IconData icon, String text, BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: context.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: context.accentColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  // Show subscription promotion dialog
  void _showSubscriptionPromotion() {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.unlimitedVehicles),
            const SizedBox(height: 16),
            Text(
                'Free users are limited to 3 vehicles. Upgrade to Premium for unlimited vehicles.'),
            const SizedBox(height: 8),
            // Use hardcoded features since we don't have specific localization keys for each feature
            ...[
              'Voice input for quick data entry',
              'Ad-free experience',
              'Unlimited vehicles',
              'Enhanced analytics and insights',
              'Cloud backup and sync'
            ].map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(feature)),
                    ],
                  ),
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final carsAsync = ref.watch(carsProvider);

    return LoadingOverlay(
      isLoading: _isLoading,
      // Use a simple hardcoded message
      message: _isLoading ? "Saving car..." : null,
      child: Scaffold(
        backgroundColor: context.containerBackgroundColor,
        appBar: AppBarFactory.modal(
          title: l10n.addNewCar,
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                context.containerBackgroundColor,
                context.isDarkMode
                    ? Color(0xFF2A2727) // Slightly darker shade for dark mode
                    : Colors.grey.shade100, // Lighter shade for light mode
              ],
            ),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Welcome section for first-time users
                    carsAsync.when(
                      data: (cars) => cars.isEmpty ? _buildWelcomeSection(l10n) : const SizedBox.shrink(),
                      loading: () => const SizedBox.shrink(),
                      error: (_, __) => const SizedBox.shrink(),
                    ),

                    // Car Image Picker
                    _buildCarImagePicker(l10n),

                    // Form Fields with animations
                    _buildAnimatedFormFields(l10n),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCarImagePicker(S l10n) {
    final carsAsync = ref.watch(carsProvider);
    final isFirstCar = carsAsync.when(
      data: (cars) => cars.isEmpty,
      loading: () => false,
      error: (_, __) => false,
    );

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -0.2),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      )),
      child: FadeTransition(
        opacity: _animationController.drive(
          Tween<double>(begin: 0.0, end: 1.0)
              .chain(CurveTween(curve: const Interval(0.0, 0.5))),
        ),
        child: Column(
          children: [
            // Enhanced image picker with first-time user guidance
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: isFirstCar && _selectedImages.isEmpty
                    ? Border.all(
                        color: context.accentColor.withOpacity(0.3),
                        width: 2,
                      )
                    : null,
              ),
              child: SizedBox(
                height: 250,
                child: ImageCarousel(
                  localImages: _selectedImages,
                  onAddImageTap: _pickImage,
                  onDeleteImageTap: (index) {
                    setState(() {
                      _selectedImages.removeAt(index);
                      if (_selectedImages.isEmpty) {
                        _isImageSelected = false;
                      }
                    });
                  },
                  enableAdd: true,
                  enableDelete: true,
                  imageFit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),

            // First-time user hint for image picker
            if (isFirstCar && _selectedImages.isEmpty)
              Container(
                margin: const EdgeInsets.only(top: 12),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: context.accentColor.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 16,
                      color: context.accentColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      l10n.tapToAddPhotos,
                      style: TextStyle(
                        color: context.accentColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedFormFields(S l10n) {
    return Column(
      children: [
        // Car Make Dropdown with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.2, 0.7, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.2, 0.7))),
            ),
            child: _buildFormField(
              child: CustomTextField(
                controller: _makeController,
                labelText: l10n.make,
                prefixIcon: Icon(Icons.car_repair, color: context.accentColor),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterCarMake;
                  }
                  return null;
                },
              ),
            ),
          ),
        ),

        // Model field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.3, 0.8, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.3, 0.8))),
            ),
            child: _buildFormField(
              child: CustomTextField(
                controller: _modelController,
                labelText: l10n.model,
                prefixIcon: Icon(Icons.label, color: context.accentColor),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterCarModel;
                  }
                  return null;
                },
              ),
            ),
          ),
        ),

        // Year field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.4, 0.9, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.4, 0.9))),
            ),
            child: _buildFormField(
              child: CustomTextField(
                controller: _yearController,
                labelText: l10n.year,
                keyboardType: TextInputType.number,
                prefixIcon:
                    Icon(Icons.calendar_today, color: context.accentColor),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterCarYear;
                  }
                  final year = int.tryParse(value);
                  if (year == null ||
                      year < 1900 ||
                      year > DateTime.now().year + 1) {
                    return l10n.pleaseEnterValidYear;
                  }
                  return null;
                },
              ),
            ),
          ),
        ),

        // Current Mileage field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.5, 1.0))),
            ),
            child: _buildFormField(
              child: CustomTextField(
                controller: _currentMileageController,
                labelText: l10n.currentMileage,
                keyboardType: TextInputType.number,
                prefixIcon: Icon(Icons.speed, color: context.accentColor),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterCurrentMileage;
                  }
                  final mileage = int.tryParse(value);
                  if (mileage == null || mileage < 0) {
                    return l10n.invalidMileage;
                  }
                  return null;
                },
              ),
            ),
          ),
        ),

        // Last Oil Change Mileage field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.6, 1.0))),
            ),
            child: _buildFormField(
              child: CustomTextField(
                controller: _lastOilChangeMileageController,
                labelText: l10n.lastOilChangeMileage,
                keyboardType: TextInputType.number,
                prefixIcon: Icon(Icons.oil_barrel, color: context.accentColor),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return l10n.pleaseEnterLastOilChangeMileage;
                  }
                  final mileage = int.tryParse(value);
                  if (mileage == null || mileage < 0) {
                    return l10n.invalidMileage;
                  }
                  return null;
                },
              ),
            ),
          ),
        ),

        // Last Oil Change Date field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.7, 1.0))),
            ),
            child: _buildFormField(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: context.accentColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    leading: Icon(Icons.event, color: context.accentColor),
                    title: Text(
                      l10n.lastOilChange,
                      style:
                          TextStyle(color: context.accentColor, fontSize: 14),
                    ),
                    subtitle: Text(
                      _lastOilChangeDate.toString().split(' ')[0],
                      style: TextStyle(
                          color: context.primaryTextColor, fontSize: 16),
                    ),
                    trailing: IconButton(
                      icon: Icon(Icons.calendar_today,
                          color: context.accentColor),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _lastOilChangeDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.dark(
                                  primary: context.accentColor,
                                  onPrimary: context.isDarkMode
                                      ? Colors.black
                                      : Colors.white,
                                  surface: context.containerBackgroundColor,
                                  onSurface: context.accentColor,
                                ),
                                dialogTheme: DialogTheme(
                                    backgroundColor:
                                        context.containerBackgroundColor),
                              ),
                              child: child!,
                            );
                          },
                        );
                        if (date != null) {
                          setState(() => _lastOilChangeDate = date);
                        }
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),

        // License Expiry Date field with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.75, 1.0,
                curve: Curves.easeOut), // Adjusted interval
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0).chain(CurveTween(
                  curve: const Interval(0.75, 1.0))), // Adjusted interval
            ),
            child: _buildFormField(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: context.accentColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    leading: Icon(Icons.event_available,
                        color: context.accentColor), // Changed Icon
                    title: Text(
                      l10n.licenseExpiryDate, // TODO: Add to l10n
                      style:
                          TextStyle(color: context.accentColor, fontSize: 14),
                    ),
                    subtitle: Text(
                      _licenseExpiryDate?.toString().split(' ')[0] ??
                          l10n.notSet, // TODO: Add to l10n
                      style: TextStyle(
                          color: context.primaryTextColor, fontSize: 16),
                    ),
                    trailing: IconButton(
                      icon: Icon(Icons.calendar_today,
                          color: context.accentColor),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _licenseExpiryDate ?? DateTime.now(),
                          firstDate: DateTime.now().subtract(const Duration(
                              days:
                                  365)), // Can be in the past if already expired
                          lastDate: DateTime.now().add(const Duration(
                              days: 365 * 10)), // Max 10 years in future
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: ColorScheme.dark(
                                  primary: context.accentColor,
                                  onPrimary: context.isDarkMode
                                      ? Colors.black
                                      : Colors.white,
                                  surface: context.containerBackgroundColor,
                                  onSurface: context.accentColor,
                                ),
                                dialogTheme: DialogTheme(
                                    backgroundColor:
                                        context.containerBackgroundColor),
                              ),
                              child: child!,
                            );
                          },
                        );
                        if (date != null) {
                          setState(() => _licenseExpiryDate = date);
                        }
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),

        // Oil Change Interval Section
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.2),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.8, 1.0, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.8, 1.0))),
            ),
            child: _buildFormField(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Oil Change Interval Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.accentColor),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.black.withOpacity(0.1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.repeat, color: context.accentColor),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                l10n.oilChangeInterval,
                                style: TextStyle(
                                  color: context.accentColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          l10n.selectRecommendedInterval,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color:
                                context.secondaryAccentColor.withOpacity(0.2),
                            border:
                                Border.all(color: context.secondaryAccentColor),
                          ),
                          child: Text(
                            l10n.selected(
                                "${_oilChangeMileageInterval.toInt()} ${l10n.distance}"),
                            style: TextStyle(
                              color: context.primaryTextColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          childAspectRatio: 2.2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          children: [
                            _buildIntervalRadio(3000, l10n),
                            _buildIntervalRadio(5000, l10n),
                            _buildIntervalRadio(7500, l10n),
                            _buildIntervalRadio(10000, l10n),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Time-based Oil Change Interval
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.accentColor),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.black.withOpacity(0.1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.calendar_today,
                                color: context.accentColor),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                S.of(context).timeBasedOilChangeInterval,
                                style: TextStyle(
                                  color: context.accentColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Tooltip(
                              message:
                                  S.of(context).timeBasedIntervalExplanation,
                              child: Icon(Icons.info_outline,
                                  color: context.accentColor, size: 16),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          S.of(context).timeBasedIntervalDescription,
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: context.secondaryAccentColor
                                      .withOpacity(0.2),
                                  border: Border.all(
                                      color: context.secondaryAccentColor
                                          .withOpacity(0.5)),
                                ),
                                child: Text(
                                  l10n.selected(
                                      "$_oilChangeMonthInterval ${l10n.months}"),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: context.primaryTextColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            SizedBox(
                              width: 80,
                              height: 45,
                              child: TextFormField(
                                controller: _monthIntervalController,
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                decoration: InputDecoration(
                                  suffixText: l10n.months,
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: context.accentColor
                                            .withOpacity(0.5)),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide:
                                        BorderSide(color: context.accentColor),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 0),
                                ),
                                onChanged: (value) {
                                  final intValue = int.tryParse(value);
                                  if (intValue != null &&
                                      intValue >= 1 &&
                                      intValue <= 24) {
                                    setState(() {
                                      _oilChangeMonthInterval = intValue;
                                    });
                                  }
                                },
                                validator: _validateMonthInterval,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 4,
                          childAspectRatio: 2.5,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          children: [
                            _buildMonthChip(3, l10n),
                            _buildMonthChip(6, l10n),
                            _buildMonthChip(9, l10n),
                            _buildMonthChip(12, l10n),
                            _buildMonthChip(1, l10n),
                            _buildMonthChip(2, l10n),
                            _buildMonthChip(4, l10n),
                            _buildMonthChip(8, l10n),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                context.secondaryAccentColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                                color: context.secondaryAccentColor
                                    .withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.notifications_active,
                                  color: context.accentColor, size: 16),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  S.of(context).notificationExplanation,
                                  style: TextStyle(
                                    color: context.secondaryTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Save Button with animation
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.2),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.9, 1.0, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: _animationController.drive(
              Tween<double>(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: const Interval(0.9, 1.0))),
            ),
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    context.secondaryAccentColor,
                    context.secondaryAccentColor
                        .withOpacity(0.8), // Lighter shade
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: context.secondaryAccentColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _saveCar,
                  borderRadius: BorderRadius.circular(12),
                  child: Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.save,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          l10n.saveCar,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({required Widget child}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: child,
    );
  }

  Widget _buildIntervalRadio(double value, S l10n) {
    final bool isSelected = _oilChangeMileageInterval == value;
    return InkWell(
      onTap: () {
        setState(() => _oilChangeMileageInterval = value);
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.secondaryAccentColor
                : context.accentColor.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          color: isSelected
              ? context.secondaryAccentColor.withOpacity(0.2)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.scale(
              scale: 1.2,
              child: Radio<double>(
                value: value,
                groupValue: _oilChangeMileageInterval,
                activeColor: context.secondaryAccentColor,
                fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                  if (states.contains(WidgetState.selected)) {
                    return context.secondaryAccentColor;
                  }
                  return context.accentColor;
                }),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                onChanged: (newValue) {
                  setState(() => _oilChangeMileageInterval = newValue!);
                },
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                "${value.toInt()} ${l10n.distance}",
                style: TextStyle(
                  color: isSelected
                      ? context.primaryTextColor
                      : context.accentColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 12,
                ),
                overflow: TextOverflow.visible,
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthChip(int months, S l10n) {
    final isSelected = _oilChangeMonthInterval == months;
    return InkWell(
      onTap: () {
        setState(() {
          _oilChangeMonthInterval = months;
          _monthIntervalController.text = months.toString();
        });
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? context.secondaryAccentColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? context.secondaryAccentColor
                : context.accentColor.withOpacity(0.5),
          ),
        ),
        child: Center(
          child: Text(
            "$months ${l10n.months}",
            style: TextStyle(
              color: isSelected ? Colors.white : context.accentColor,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  String? _validateMonthInterval(String? value) {
    if (value == null || value.isEmpty) {
      return S.of(context).pleaseEnterCarMake;
    }
    final intValue = int.tryParse(value);
    if (intValue == null || intValue < 1 || intValue > 24) {
      return S.of(context).invalidMonthRange;
    }
    return null;
  }

  Widget _buildWelcomeSection(S l10n) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -0.3),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      )),
      child: FadeTransition(
        opacity: _animationController.drive(
          Tween<double>(begin: 0.0, end: 1.0)
              .chain(CurveTween(curve: const Interval(0.0, 0.4))),
        ),
        child: Container(
          margin: const EdgeInsets.only(bottom: 32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                context.accentColor.withOpacity(0.1),
                context.secondaryAccentColor.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: context.accentColor.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: context.accentColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // Welcome icon with pulse animation
              ScaleTransition(
                scale: _pulseAnimation,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.directions_car,
                    size: 48,
                    color: context.accentColor,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Welcome title
              Text(
                l10n.welcomeText,
                style: TextStyle(
                  color: context.accentColor,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Welcome message
              Text(
                l10n.firstCarWelcomeMessage,
                style: TextStyle(
                  color: context.secondaryTextColor,
                  fontSize: 16,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Progress indicator
              _buildProgressIndicator(l10n),
              const SizedBox(height: 16),

              // Quick tips
              _buildQuickTips(l10n),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(S l10n) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: context.secondaryAccentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: context.secondaryAccentColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 16,
            color: context.secondaryAccentColor,
          ),
          const SizedBox(width: 8),
          Text(
            l10n.firstCarStepIndicator,
            style: TextStyle(
              color: context.secondaryAccentColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickTips(S l10n) {
    final tips = [
      {
        'icon': Icons.photo_camera,
        'text': l10n.tipAddPhotos,
      },
      {
        'icon': Icons.speed,
        'text': l10n.tipEnterMileage,
      },
      {
        'icon': Icons.schedule,
        'text': l10n.tipSetIntervals,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.quickTips,
          style: TextStyle(
            color: context.accentColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...tips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  tip['icon'] as IconData,
                  size: 16,
                  color: context.accentColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  tip['text'] as String,
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        )).toList(),
      ],
    );
  }
}
