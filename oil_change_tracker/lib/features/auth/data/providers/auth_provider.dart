import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/app_check_service.dart';
import 'dart:developer' as dev;
import '../repositories/user_repository.dart';
import '../../../../services/notification_service.dart';

final authProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);

final firebaseAuthProvider = Provider<FirebaseAuth>((ref) {
  return FirebaseAuth.instance;
});

final authStateChangesProvider = StreamProvider<User?>((ref) {
  return ref.watch(firebaseAuthProvider).authStateChanges();
});

final currentUserProvider = StateProvider<UserModel?>((ref) {
  final authState = ref.watch(authStateChangesProvider);
  return authState.when(
    data: (user) => user != null ? UserModel.fromFirebaseUser(user) : null,
    loading: () => null,
    error: (_, __) => null,
  );
});

class AuthNotifier extends StateNotifier<AsyncValue<User?>> {
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;
  final Ref _ref;
  final AppCheckService _appCheckService;

  AuthNotifier(this._auth, this._googleSignIn, this._ref, this._appCheckService)
      : super(const AsyncValue<User?>.loading()) {
    _initialize();
  }

  void _initialize() {
    // Implementation of _initialize method
  }

  Future<void> _ensureAppCheckReady() async {
    dev.log('Auth: Ensuring App Check is ready');
    final appCheckService = _ref.read(appCheckServiceProvider);
    final firestore = FirebaseFirestore.instance;
    
    if (kDebugMode) {
      // In debug mode, we can skip waiting for App Check to avoid issues
      // with emulators or development builds
    } else {
      // In production, wait for App Check to be initialized
      await _ref.read(appCheckInitializationProvider.future);
      dev.log('Auth: App Check initialization completed');
      
      // Verify token availability
      final token = await FirebaseAppCheck.instance.getToken();
      dev.log('Auth: App Check token status: ${token != null}');
    }
    
    // Now proceed with sign in
    state = const AsyncValue<User?>.loading();
    
    // Sign out first to ensure a fresh authentication
    await _auth.signOut();
    await GoogleSignIn().signOut();

    try {
      dev.log('Auth: Starting Google Sign In process');
      // Configure Google Sign In
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      
      // Check if the user canceled the sign-in
      if (googleUser == null) {
        dev.log('Auth: User cancelled Google Sign In');
        state = AsyncValue<User?>.error('Sign in was cancelled by user', StackTrace.current);
        return;
      }
      
      dev.log('Auth: Google Sign In successful, getting auth details');
      // Get authentication details from Google Sign In
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Create credential from Google authentication details
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      dev.log('Auth: Signing in with Firebase');
      // Sign in with Firebase using the Google Auth credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);
      
      // Extract user details
      final User? user = userCredential.user;
      if (user == null) {
        state = AsyncValue<User?>.error('Failed to sign in: No user returned', StackTrace.current);
        return;
      }
      
      // Prepare user data for the database
      final Map<String, dynamic> userDetails = {
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName,
        'photoUrl': user.photoURL,
        'lastSignIn': DateTime.now().toIso8601String(),
      };
      
      // Create or update user document in Firestore
      await firestore.collection('users').doc(user.uid).set(userDetails, SetOptions(merge: true));
      
      dev.log('Auth: Successfully signed in user: ${userDetails['email']}');
      state = AsyncValue<User?>.data(user);
    } catch (e) {
      dev.log('Auth: Error in signInWithCredential: $e');
      
      // Special case: If the user is signed in despite error, proceed
      if (_auth.currentUser != null) {
        dev.log('Auth: User is signed in despite error, proceeding');
        state = const AsyncValue<User?>.data(null);
        return;
      }
      
      state = AsyncValue<User?>.error(e, StackTrace.current);
    }
  }

  Future<void> signIn(String email, String password) async {
    try {
      state = const AsyncValue<User?>.loading();
      
      // Sign in with Firebase Auth
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user != null) {
        // Update last login timestamp
        final userRepository = _ref.read(userRepositoryProvider);
        await userRepository.updateLastLogin(userCredential.user!.uid);
        
        // Update topics based on new login
        final notificationService = _ref.read(notificationServiceProvider);
        notificationService.refreshTopicSubscriptions();
      }
      
      state = AsyncValue<User?>.data(userCredential.user);
    } on FirebaseAuthException catch (e) {
      state = AsyncValue<User?>.error(e, StackTrace.current);
      rethrow;
    } catch (e) {
      state = AsyncValue<User?>.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> createUserWithEmailAndPassword(String email, String password) async {
    try {
      state = const AsyncValue<User?>.loading();
      await _ensureAppCheckReady();
      await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      state = const AsyncValue<User?>.data(null);
    } catch (e, st) {
      state = AsyncValue<User?>.error(e, st);
      rethrow;
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      await _ensureAppCheckReady();
    } catch (e, st) {
      state = AsyncValue<User?>.error(e, st);
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      state = const AsyncValue<User?>.loading();
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
      state = const AsyncValue<User?>.data(null);
    } catch (e, st) {
      state = AsyncValue<User?>.error(e, st);
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      state = const AsyncValue<User?>.loading();
      await _auth.sendPasswordResetEmail(email: email);
      state = const AsyncValue<User?>.data(null);
    } catch (e, st) {
      state = AsyncValue<User?>.error(e, st);
      rethrow;
    }
  }

  Future<void> updateProfile({String? displayName, String? photoURL}) async {
    try {
      state = const AsyncValue<User?>.loading();
      final user = _auth.currentUser;
      if (user != null) {
        if (displayName != null) {
          await user.updateDisplayName(displayName);
        }
        if (photoURL != null) {
          await user.updatePhotoURL(photoURL);
        }
      }
      state = const AsyncValue<User?>.data(null);
    } catch (e, st) {
      state = AsyncValue<User?>.error(e, st);
      rethrow;
    }
  }
}

final authNotifierProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<User?>>((ref) {
  final auth = ref.watch(firebaseAuthProvider);
  final googleSignIn = GoogleSignIn();
  final appCheckService = ref.read(appCheckServiceProvider);
  return AuthNotifier(auth, googleSignIn, ref, appCheckService);
});
