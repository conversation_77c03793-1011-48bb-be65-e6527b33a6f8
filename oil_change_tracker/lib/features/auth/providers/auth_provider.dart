import 'dart:async';
import 'dart:developer' as dev;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../../core/models/user_model.dart'; // Assuming UserModel has copyWith
import '../data/services/auth_service.dart';
import '../../../core/services/app_check_service.dart';
import '../../subscription/providers/subscription_provider.dart';
import 'package:flutter/foundation.dart';

final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final authStateProvider = StreamProvider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

final currentUserProvider = Provider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.currentUser;
});

// Define the main authProvider that the UI will interact with
final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<UserModel?>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final googleSignIn =
      GoogleSignIn(); // Or provide it if already available elsewhere
  final firestore = FirebaseFirestore.instance; // Or provide it
  return AuthNotifier(authService, ref, googleSignIn, firestore);
});

class AuthNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final AuthService _authService;
  final Ref _ref;
  final GoogleSignIn _googleSignIn;
  AppCheckService? _appCheckService;
  final FirebaseFirestore _firestore;
  StreamSubscription<User?>? _authStateSubscription;

  AuthNotifier(
      this._authService, this._ref, this._googleSignIn, this._firestore)
      : super(const AsyncValue.loading()) {
    _init();
  }

  Future<void> _init() async {
    if (!mounted) return;
    _appCheckService = _ref.read(appCheckServiceProvider);

    _authStateSubscription =
        _authService.authStateChanges.listen((firebaseUser) async {
      if (!mounted) return;
      if (firebaseUser != null) {
        dev.log(
            'Auth (authStateChanges): User detected: ${firebaseUser.uid}. Updating state.');
        await _updateStateWithUserData(firebaseUser.uid);
      } else {
        dev.log(
            'Auth (authStateChanges): No user detected. Setting state to null.');
        if (!mounted) return;
        state = const AsyncValue.data(null);
      }
    }, onError: (e, st) {
      dev.log('Auth (authStateChanges): Error in stream: $e');
      if (!mounted) return;
      state = AsyncValue.error(e, st);
    });

    final initialFirebaseUser = _authService.currentUser;
    if (initialFirebaseUser != null) {
      dev.log(
          'Auth (_init): Initial user found: ${initialFirebaseUser.uid}. Updating state.');
      await _updateStateWithUserData(initialFirebaseUser.uid);
    } else {
      dev.log('Auth (_init): No initial user. Setting state to null.');
      if (!mounted) return;
      state = const AsyncValue.data(null);
    }
  }

  Future<UserModel?> _fetchOrCreateUserDocument(User firebaseUser,
      {String? displayNameIfNotSet}) async {
    final userDocRef = _firestore.collection('users').doc(firebaseUser.uid);
    try {
      final userDoc = await userDocRef.get();

      if (userDoc.exists && userDoc.data() != null) {
        dev.log(
            'Auth (_fetchOrCreateUserDocument): Found existing Firestore doc for ${firebaseUser.uid}');
        UserModel model = UserModel.fromFirestore(userDoc);

        // Sync isEmailVerified from Firebase Auth to Firestore if different
        if (model.isEmailVerified != firebaseUser.emailVerified) {
          dev.log(
              'Auth (_fetchOrCreateUserDocument): Discrepancy in email verification for ${firebaseUser.uid}. Firestore: ${model.isEmailVerified}, Firebase Auth: ${firebaseUser.emailVerified}. Updating Firestore.');
          await userDocRef.update({
            'isEmailVerified': firebaseUser.emailVerified,
            'updatedAt': FieldValue.serverTimestamp()
          });
          // Assuming UserModel has a copyWith method
          model = model.copyWith(isEmailVerified: firebaseUser.emailVerified);
        }
        return model;
      } else {
        final newDisplayName = displayNameIfNotSet ??
            firebaseUser.displayName ??
            firebaseUser.email?.split('@')[0] ??
            'User';
        dev.log(
            'Auth (_fetchOrCreateUserDocument): Firestore doc NOT found for ${firebaseUser.uid}. Creating with name "$newDisplayName", verified: ${firebaseUser.emailVerified}.');

        final newUserModel = UserModel(
          id: firebaseUser.uid,
          email: firebaseUser.email ?? '',
          displayName: newDisplayName,
          photoUrl: firebaseUser.photoURL,
          isEmailVerified:
              firebaseUser.emailVerified, // Use current Firebase Auth status
        );

        final Map<String, dynamic> userDataToSet = {
          'id': newUserModel.id,
          'email': newUserModel.email,
          'displayName': newUserModel.displayName,
          if (newUserModel.photoUrl != null) 'photoUrl': newUserModel.photoUrl,
          'isEmailVerified': newUserModel.isEmailVerified,
          'createdAt': FieldValue.serverTimestamp(),
          'lastSignIn': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        };
        await userDocRef.set(userDataToSet);
        dev.log(
            'Auth (_fetchOrCreateUserDocument): Created Firestore doc for ${firebaseUser.uid}.');

        // Start trial for new user
        try {
          final subscriptionNotifier = _ref.read(subscriptionProvider.notifier);
          await subscriptionNotifier.startTrialForNewUser(firebaseUser.uid);
          dev.log(
              'Auth (_fetchOrCreateUserDocument): Started trial for new user ${firebaseUser.uid}');
        } catch (e) {
          dev.log(
              'Auth (_fetchOrCreateUserDocument): Error starting trial for new user: $e');
          // Don't fail user creation if trial start fails
        }

        return newUserModel;
      }
    } catch (e) {
      dev.log(
          'Auth (_fetchOrCreateUserDocument): Firestore error for ${firebaseUser.uid}: $e. Falling back to Firebase Auth data.');
      final fallbackDisplayName = displayNameIfNotSet ??
          firebaseUser.displayName ??
          firebaseUser.email?.split('@')[0] ??
          'User';
      return UserModel(
        // Direct constructor call
        id: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        displayName: fallbackDisplayName,
        photoUrl: firebaseUser.photoURL,
        isEmailVerified: firebaseUser.emailVerified,
      );
    }
  }

  Future<void> _updateStateWithUserData(String uid,
      {String? displayNameIfNotSet}) async {
    if (!mounted) return;

    dev.log(
        'Auth (_updateStateWithUserData): Updating state with user data for $uid');

    try {
      // Get both Firestore user doc and Firebase Auth user to merge data
      var userDoc = await _firestore
          .collection('users')
          .doc(uid)
          .get(GetOptions(source: Source.server));
      var authUser = _authService.currentUser;

      if (authUser == null) {
        dev.log(
            'Auth (_updateStateWithUserData): Firebase Auth user is null for $uid');
        state = const AsyncValue.data(null);
        return;
      }

      if (!userDoc.exists) {
        dev.log(
            'Auth (_updateStateWithUserData): Creating new user document in Firestore for $uid');

        // Create a new user document if it does not exist
        // Use the Firebase Auth user info to populate the document
        var userModel = UserModel.fromFirebaseUser(authUser);

        // Apply displayNameIfNotSet if needed
        if (displayNameIfNotSet != null &&
            (userModel.displayName.isEmpty ||
                userModel.displayName == 'User')) {
          userModel = userModel.copyWith(displayName: displayNameIfNotSet);
          dev.log(
              'Auth (_updateStateWithUserData): Applied displayNameIfNotSet: $displayNameIfNotSet');
        }

        await _firestore.collection('users').doc(uid).set(
            userModel.toFirestore()
              ..addAll({
                'createdAt': FieldValue.serverTimestamp(),
                'updatedAt': FieldValue.serverTimestamp(),
              }),
            SetOptions(merge: true));

        dev.log(
            'Auth (_updateStateWithUserData): Created new user document, getting updated doc');
        // Re-fetch the document we just created to confirm it worked
        userDoc = await _firestore
            .collection('users')
            .doc(uid)
            .get(GetOptions(source: Source.server));
      }

      UserModel? userModel;
      dev.log(
          'Auth (_updateStateWithUserData): Parsing user document for $uid');

      try {
        // First try parsing with the standard method
        userModel = UserModel.fromFirestore(userDoc);
        dev.log('Auth (_updateStateWithUserData): Standard parsing successful');

        // Check if license expiry date was properly parsed
        if (userDoc.data()?.containsKey('licenseExpiryDate') == true &&
            userModel.licenseExpiryDate == null) {
          dev.log(
              'Auth (_updateStateWithUserData): License expiry date field exists but was not parsed, trying fallback');
          // If document has licenseExpiryDate but model doesn't, use fallback method
          userModel = _parseUserWithFallback(userDoc, authUser);
        }
      } catch (e) {
        dev.log(
            'Auth (_updateStateWithUserData): Error parsing user document: $e');
        // Fallback to manual parsing if standard method fails
        userModel = _parseUserWithFallback(userDoc, authUser);
      }

      if (userModel == null) {
        dev.log(
            'Auth (_updateStateWithUserData): Failed to parse user model for $uid');
        // Emergency fallback if all parsing fails - create a minimal user model from Auth data
        userModel = UserModel.fromFirebaseUser(authUser);

        // Apply displayNameIfNotSet if needed for the fallback model too
        if (displayNameIfNotSet != null &&
            (userModel.displayName.isEmpty ||
                userModel.displayName == 'User')) {
          userModel = userModel.copyWith(displayName: displayNameIfNotSet);
          dev.log(
              'Auth (_updateStateWithUserData): Applied displayNameIfNotSet to fallback model: $displayNameIfNotSet');
        }
      }

      // Make sure we have license date information in logs
      if (userModel.licenseExpiryDate != null) {
        dev.log(
            'Auth (_updateStateWithUserData): User has license expiry date: ${userModel.licenseExpiryDate}');
      } else {
        var docData = userDoc.data();
        if (docData != null && docData.containsKey('licenseExpiryDate')) {
          dev.log(
              'Auth (_updateStateWithUserData): Document has licenseExpiryDate but failed to parse: ${docData['licenseExpiryDate']} (${docData['licenseExpiryDate'].runtimeType})');
        } else {
          dev.log(
              'Auth (_updateStateWithUserData): No license expiry date in document');
        }
      }

      // NEW: Keep Firestore email verification status in sync with Firebase Auth
      if (userModel.isEmailVerified != authUser.emailVerified) {
        dev.log(
            'Auth (_updateStateWithUserData): Email verification mismatch detected. Firestore: ${userModel.isEmailVerified}, Auth: ${authUser.emailVerified}. Updating Firestore.');
        try {
          await _firestore.collection('users').doc(uid).update({
            'isEmailVerified': authUser.emailVerified,
            'updatedAt': FieldValue.serverTimestamp(),
          });
          userModel =
              userModel.copyWith(isEmailVerified: authUser.emailVerified);
        } catch (e) {
          dev.log(
              'Auth (_updateStateWithUserData): Failed to update isEmailVerified field in Firestore: $e');
        }
      }

      if (!mounted) return;
      state = AsyncValue.data(userModel);
      dev.log(
          'Auth (_updateStateWithUserData): State updated successfully for $uid');
    } catch (e) {
      dev.log(
          'Auth (_updateStateWithUserData): Error updating state with user data: $e');
      if (!mounted) return;
      // Instead of returning an error state, we'll try to at least return the Firebase Auth user data
      try {
        var authUser = _authService.currentUser;
        if (authUser != null) {
          var userModel = UserModel.fromFirebaseUser(authUser);

          // Apply displayNameIfNotSet to fallback auth model too
          if (displayNameIfNotSet != null &&
              (userModel.displayName.isEmpty ||
                  userModel.displayName == 'User')) {
            userModel = userModel.copyWith(displayName: displayNameIfNotSet);
          }

          state = AsyncValue.data(userModel);
          dev.log(
              'Auth (_updateStateWithUserData): Fallback to Auth user data successful');
        } else {
          state = AsyncValue.error(e, StackTrace.current);
          dev.log(
              'Auth (_updateStateWithUserData): No fallback available, returning error state');
        }
      } catch (fallbackError) {
        state = AsyncValue.error(e, StackTrace.current);
        dev.log(
            'Auth (_updateStateWithUserData): Fallback failed: $fallbackError, returning original error');
      }
    }
  }

  // Helper method to manually parse user data with fallback approaches
  UserModel? _parseUserWithFallback(DocumentSnapshot userDoc, User authUser) {
    dev.log(
        'Auth (_parseUserWithFallback): Trying to parse user with fallback');
    try {
      final data = userDoc.data() as Map<String, dynamic>?;
      if (data == null) {
        dev.log('Auth (_parseUserWithFallback): User document data is null');
        return null;
      }

      // Handle the licenseExpiryDate specifically
      DateTime? licenseDate;
      if (data.containsKey('licenseExpiryDate')) {
        final licenseValue = data['licenseExpiryDate'];

        try {
          if (licenseValue is int) {
            licenseDate = DateTime.fromMillisecondsSinceEpoch(licenseValue);
            dev.log(
                'Auth (_parseUserWithFallback): Parsed license date from int: $licenseDate');
          } else if (licenseValue is Timestamp) {
            licenseDate = licenseValue.toDate();
            dev.log(
                'Auth (_parseUserWithFallback): Parsed license date from Timestamp: $licenseDate');
          } else if (licenseValue is String) {
            licenseDate = DateTime.parse(licenseValue);
            dev.log(
                'Auth (_parseUserWithFallback): Parsed license date from String: $licenseDate');
          } else {
            dev.log(
                'Auth (_parseUserWithFallback): Unhandled license date type: ${licenseValue.runtimeType}');
          }
        } catch (e) {
          dev.log(
              'Auth (_parseUserWithFallback): Error parsing license date: $e');
        }
      }

      // Try to construct user model with the parsed data
      return UserModel(
        id: userDoc.id,
        displayName:
            data['displayName'] as String? ?? authUser.displayName ?? 'User',
        email: data['email'] as String? ?? authUser.email ?? '',
        photoUrl: data['photoUrl'] as String? ?? authUser.photoURL,
        licenseExpiryDate: licenseDate,
        licenseExpiryNotificationEnabled:
            data['licenseExpiryNotificationEnabled'] as bool? ?? false,
        isEmailVerified:
            data['isEmailVerified'] as bool? ?? authUser.emailVerified,
        providerId: data['providerId'] as String? ??
            (authUser.providerData.isNotEmpty
                ? authUser.providerData.first.providerId
                : 'firebase'),
        providerData: (data['providerData'] as List<dynamic>?)
            ?.map((e) => e as Map<String, dynamic>)
            .toList(),
      );
    } catch (e) {
      dev.log('Auth (_parseUserWithFallback): Fallback parsing error: $e');
      return null;
    }
  }

  Future<void> _ensureAppCheckReady() async {
    // ... (keep existing _ensureAppCheckReady method content)
    try {
      dev.log('Auth: Ensuring App Check is ready');
      if (_appCheckService == null) {
        dev.log('Auth: Getting App Check service from provider');
        _appCheckService = _ref.read(appCheckServiceProvider);
      }

      if (!_appCheckService!.isInitialized) {
        dev.log('Auth: App Check not initialized, initializing now');
        try {
          await _ref.read(appCheckTokenProvider.future);
          dev.log('Auth: App Check token obtained successfully');
        } catch (tokenError) {
          dev.log('Auth: Error getting App Check token: $tokenError');
          await _ref.read(appCheckInitializationProvider.future);
          dev.log('Auth: App Check initialized without token');
        }
      }
      dev.log('Auth: App Check is ready');
    } catch (e) {
      dev.log('Auth: Error ensuring App Check ready: $e');
      if (kDebugMode) {
        dev.log('Auth: Continuing in debug mode despite App Check error');
        return;
      }
      rethrow;
    }
  }

  Future<void> signUpWithEmail({
    required String name,
    required String email,
    required String password,
  }) async {
    if (!mounted) return;
    state = const AsyncValue.loading();
    try {
      await _ensureAppCheckReady();
      dev.log(
          'Auth (signUpWithEmail): Attempting for $email with name "$name"');

      // Get credential from auth service
      final credential =
          await _authService.signUpWithEmail(email: email, password: password);
      final firebaseUser = credential.user;

      if (firebaseUser == null) {
        throw Exception(
            'Failed to create Firebase user account for $email during sign-up.');
      }
      dev.log(
          'Auth (signUpWithEmail): Firebase user created for $email. UID: ${firebaseUser.uid}');

      // Update display name separately since the service doesn't support it directly
      try {
        await firebaseUser.updateDisplayName(name);
        dev.log(
            'Auth (signUpWithEmail): Firebase Auth profile display name updated to "$name" for ${firebaseUser.uid}');
      } catch (profileError) {
        dev.log(
            'Auth (signUpWithEmail): Error updating Firebase Auth profile display name for ${firebaseUser.uid}: $profileError. Continuing...');
      }

      // Send verification email
      try {
        dev.log(
            'Auth (signUpWithEmail): Sending verification email to ${firebaseUser.email}');
        await firebaseUser.sendEmailVerification();
        dev.log(
            'Auth (signUpWithEmail): Verification email sent to ${firebaseUser.email}');
      } catch (verificationError) {
        dev.log(
            'Auth (signUpWithEmail): Error sending verification email to ${firebaseUser.email}: $verificationError. User will need to resend manually.');
        // Optionally, set an error state or a flag that UI can pick up to inform the user.
        // For now, just logging.
      }

      await _updateStateWithUserData(firebaseUser.uid,
          displayNameIfNotSet: name);
      dev.log(
          'Auth (signUpWithEmail): Process completed for $email. AuthNotifier state updated.');
    } catch (e, st) {
      dev.log('Auth (signUpWithEmail): Error for $email: $e');
      if (!mounted) return;
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    if (!mounted) return;
    state = const AsyncValue.loading();
    try {
      dev.log('Auth (signInWithEmail): Attempting for $email');
      await _ensureAppCheckReady(); // Ensure AppCheck is ready before sign-in attempts

      final credential = await _authService.signInWithEmail(email, password);
      final firebaseUser = credential.user;

      if (firebaseUser == null) {
        throw Exception(
            'Firebase authentication successful for $email but no user object returned.');
      }
      dev.log(
          'Auth (signInWithEmail): Firebase Auth successful for $email. UID: ${firebaseUser.uid}');

      await _updateStateWithUserData(firebaseUser.uid);
      dev.log(
          'Auth (signInWithEmail): Process completed for $email. AuthNotifier state updated.');
    } on FirebaseAuthException catch (e) {
      dev.log(
          'Auth (signInWithEmail): FirebaseAuthException for $email: ${e.code} - ${e.message}');
      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'No user found with this email.';
          break;
        case 'wrong-password':
          errorMessage = 'Wrong password provided.';
          break;
        case 'invalid-email':
          errorMessage = 'The email address is invalid.';
          break;
        case 'user-disabled':
          errorMessage = 'This user account has been disabled.';
          break;
        case 'invalid-credential':
          errorMessage =
              'Invalid credentials. Please check your email and password.';
          break;
        default:
          errorMessage = 'Authentication failed. Please try again.';
      }
      if (!mounted) return;
      state = AsyncValue.error(Exception(errorMessage), StackTrace.current);
    } catch (e, st) {
      dev.log('Auth (signInWithEmail): Unexpected error for $email: $e');
      if (!mounted) return;
      state = AsyncValue.error(
          Exception('An unexpected error occurred. Please try again.'), st);
    }
  }

  Future<void> signInWithGoogle() async {
    if (!mounted) return;
    state = const AsyncValue.loading();
    try {
      await _ensureAppCheckReady();
      dev.log('Auth (signInWithGoogle): Beginning flow.');

      // It's important to sign out from Firebase Auth first if there's an existing session
      // that might conflict, especially if it's an anonymous user.
      await _authService.signOut();
      await _googleSignIn.signOut(); // Clear previous Google sign-in attempts
      dev.log(
          'Auth (signInWithGoogle): Signed out existing Firebase and Google sessions.');

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        dev.log('Auth (signInWithGoogle): User cancelled Google Sign In.');
        if (!mounted) return;
        state = const AsyncValue.data(
            null); // User explicitly cancelled, not an error.
        return;
      }
      dev.log(
          'Auth (signInWithGoogle): Google user selected: ${googleUser.email}, Name: ${googleUser.displayName}');

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final UserCredential userCredential =
          await _authService.signInWithCredential(credential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw Exception(
            'Firebase Google sign-in successful but no Firebase user object returned.');
      }
      dev.log(
          'Auth (signInWithGoogle): Firebase sign-in successful. UID: ${firebaseUser.uid}');

      await _updateStateWithUserData(firebaseUser.uid,
          displayNameIfNotSet: googleUser.displayName);
      dev.log(
          'Auth (signInWithGoogle): Process completed. AuthNotifier state updated.');
    } catch (e, st) {
      dev.log('Auth (signInWithGoogle): Error: $e');
      if (!mounted) return;
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> signOut() async {
    if (!mounted) return;
    state = const AsyncValue.loading();
    try {
      dev.log('Auth (signOut): Attempting sign out.');
      await _authService.signOut();
      await _googleSignIn.signOut();
      dev.log(
          'Auth (signOut): Sign out from AuthService and GoogleSignIn completed.');

      // The authStateChanges listener should pick this up and set state to null.
      // For immediate feedback, we can set it here too, but it might be redundant.
      if (!mounted) return;
      state = const AsyncValue.data(null);
    } catch (e, st) {
      dev.log('Auth (signOut): Error: $e');
      if (!mounted) return;
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> resetPassword(String email) async {
    // No state change here, just forward to service
    try {
      await _authService.resetPassword(email);
    } catch (e) {
      dev.log('Auth (resetPassword): Error: $e');
      rethrow;
    }
  }

  Future<void> sendEmailVerification() async {
    // No state change here, just forward to service
    try {
      dev.log('Auth (sendEmailVerification): Sending email verification');
      final result = await _authService.sendEmailVerification();
      if (result['success'] != true) {
        throw Exception(
            result['message'] ?? 'Failed to send verification email');
      }
      dev.log(
          'Auth (sendEmailVerification): Email verification sent successfully');
    } catch (e) {
      dev.log('Auth (sendEmailVerification): Error: $e');
      rethrow;
    }
  }

  Future<bool> checkEmailVerified() async {
    dev.log('Auth (checkEmailVerified): Checking email verification status');
    User? firebaseUser = _authService.currentUser;

    if (firebaseUser == null) {
      dev.log('Auth (checkEmailVerified): No user logged in.');
      return false;
    }

    try {
      dev.log(
          'Auth (checkEmailVerified): Attempting token refresh and user reload for ${firebaseUser.uid}');
      await firebaseUser.getIdToken(true); // Refresh token
      await firebaseUser.reload(); // Reload user data from Firebase
      dev.log(
          'Auth (checkEmailVerified): Token refreshed and user reloaded for ${firebaseUser.uid}');

      // Get the potentially updated user instance from auth service AFTER reload
      firebaseUser = _authService.currentUser;
      if (firebaseUser == null) {
        dev.log('Auth (checkEmailVerified): User became null after reload.');
        return false; // Should not happen if reload was successful on an existing user
      }

      final isVerified = firebaseUser.emailVerified;
      dev.log(
          'Auth (checkEmailVerified): Email verification status after reload: $isVerified for ${firebaseUser.uid}');

      if (isVerified) {
        // This will update Firestore via _fetchOrCreateUserDocument if needed,
        // and then update the AuthNotifier's state.
        await _updateStateWithUserData(firebaseUser.uid);
        dev.log(
            'Auth (checkEmailVerified): State update triggered after successful verification for ${firebaseUser.uid}');
      }
      return isVerified;
    } catch (e) {
      dev.log(
          'Auth (checkEmailVerified): Error during token refresh/reload for ${firebaseUser?.uid}: $e'); // Added ?.
      if (e.toString().contains('PigeonUserInfo') ||
          e.toString().contains('List<Object?>')) {
        dev.log(
            'Auth (checkEmailVerified): Caught cast error. Attempting to get fresh user state.');
        final freshFirebaseUser = FirebaseAuth.instance.currentUser;
        if (freshFirebaseUser != null) {
          try {
            await freshFirebaseUser.reload(); // Reload this fresh instance too
            final isVerified = freshFirebaseUser.emailVerified;
            dev.log(
                'Auth (checkEmailVerified): Email verification status from fresh (reloaded) user ${freshFirebaseUser.uid}: $isVerified');
            if (isVerified) {
              await _updateStateWithUserData(freshFirebaseUser.uid);
            }
            return isVerified;
          } catch (freshReloadError) {
            dev.log(
                'Auth (checkEmailVerified): Error reloading fresh user instance ${freshFirebaseUser.uid}: $freshReloadError. Checking non-reloaded status.');
            final isVerified = freshFirebaseUser
                .emailVerified; // Fallback to non-reloaded status
            if (isVerified) {
              // If even this says verified, try to update state
              await _updateStateWithUserData(freshFirebaseUser.uid);
            }
            return isVerified;
          }
        } else {
          dev.log(
              'Auth (checkEmailVerified): Fresh user instance is null after cast error.');
          return false;
        }
      }
      // For other errors, return current (potentially stale) status from the initial firebaseUser object
      dev.log(
          'Auth (checkEmailVerified): Falling back to initial user.emailVerified after error: ${firebaseUser?.emailVerified}'); // Added ?.
      return firebaseUser?.emailVerified ?? false; // Added ?. and ?? false
    }
  }

  // This helper is not strictly needed anymore if _updateStateWithUserData handles Firestore updates via _fetchOrCreateUserDocument
  // However, if direct update is preferred for just this flag, it can be kept.
  // For now, _fetchOrCreateUserDocument handles ensuring Firestore `isEmailVerified` matches Firebase Auth.
  // Future<void> _updateFirestoreVerificationStatus(String uid, bool isVerified) async { ... }

  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (!mounted) return;
    state = const AsyncValue.loading();
    try {
      dev.log('Auth (updatePassword): Starting password update process');
      await _authService.updatePassword(
          currentPassword: currentPassword, newPassword: newPassword);
      dev.log(
          'Auth (updatePassword): Password change successful via AuthService.');

      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        await _updateStateWithUserData(currentUser.uid);
        dev.log(
            'Auth (updatePassword): State updated after password change for ${currentUser.uid}');
      } else {
        if (!mounted) return;
        dev.log(
            'Auth (updatePassword): Current user is null after password update. This is unexpected.');
        state = const AsyncValue.data(null);
      }
    } catch (e, st) {
      dev.log('Auth (updatePassword): Error: $e');

      // Check if this is a PigeonUserDetails error which might actually be a success
      if (e.toString().contains('PigeonUserDetails') ||
          e.toString().contains('List<Object?>')) {
        dev.log(
            'Auth (updatePassword): Detected PigeonUserDetails error, trying to recover state');

        // Try to refresh the user state
        final currentUser = _authService.currentUser;
        if (currentUser != null && mounted) {
          try {
            // Force refresh token
            dev.log(
                'Auth (updatePassword): Refreshing token for ${currentUser.uid}');
            await currentUser.getIdToken(true);

            // Try to update state with current user data
            dev.log(
                'Auth (updatePassword): Refreshing user state for ${currentUser.uid}');
            await _updateStateWithUserData(currentUser.uid);
            dev.log(
                'Auth (updatePassword): Successfully recovered state after PigeonUserDetails error');

            // Success case - don't set error state
            return;
          } catch (refreshError) {
            dev.log(
                'Auth (updatePassword): Error during recovery: $refreshError');

            // If we still have a PigeonUserDetails error, try one last approach
            if (refreshError.toString().contains('PigeonUserDetails') ||
                refreshError.toString().contains('List<Object?>')) {
              try {
                // Try using another instance of FirebaseAuth
                dev.log(
                    'Auth (updatePassword): Using fresh FirebaseAuth instance');
                final freshUser = FirebaseAuth.instance.currentUser;
                if (freshUser != null) {
                  await _updateStateWithUserData(freshUser.uid);
                  dev.log(
                      'Auth (updatePassword): State refreshed with fresh FirebaseAuth instance');
                  return;
                }
              } catch (finalError) {
                dev.log(
                    'Auth (updatePassword): Final recovery attempt failed: $finalError');
                // Fall through to error state
              }
            }
          }
        } else {
          dev.log(
              'Auth (updatePassword): Current user is null during recovery or provider unmounted');
        }
      }

      if (!mounted) return;
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateDisplayName(String displayName) async {
    if (!mounted) return;
    state = const AsyncValue.loading();

    final firebaseUser = _authService.currentUser;
    if (firebaseUser == null) {
      if (!mounted) return;
      state = AsyncValue.error(
          Exception('User not authenticated for display name update'),
          StackTrace.current);
      return;
    }
    dev.log(
        'Auth (updateDisplayName): Attempting for ${firebaseUser.uid} to "$displayName"');

    // Compare with current Firebase Auth display name.
    if (displayName.trim() == firebaseUser.displayName?.trim()) {
      dev.log(
          'Auth (updateDisplayName): Name unchanged ("${displayName.trim()}") compared to Firebase Auth. Forcing Firestore sync and state refresh.');
      // Ensure Firestore is also up-to-date with this name.
      try {
        await _firestore.collection('users').doc(firebaseUser.uid).set({
          'displayName': displayName.trim(),
          'updatedAt': FieldValue.serverTimestamp()
        }, SetOptions(merge: true));
        await _updateStateWithUserData(firebaseUser.uid,
            displayNameIfNotSet: displayName.trim());
      } catch (e, st) {
        if (!mounted) return;
        dev.log(
            'Auth (updateDisplayName): Error during Firestore sync for unchanged name: $e');
        state = AsyncValue.error(e, st);
      }
      return;
    }

    try {
      // Update the display name for all users, including Google users
      await firebaseUser.updateDisplayName(displayName);
      dev.log(
          'Auth (updateDisplayName): Firebase Auth display name updated for ${firebaseUser.uid}');

      // Update Firestore directly. This is crucial.
      await _firestore.collection('users').doc(firebaseUser.uid).update({
        'displayName': displayName,
        'updatedAt': FieldValue.serverTimestamp()
      });
      dev.log(
          'Auth (updateDisplayName): Firestore display name directly updated for ${firebaseUser.uid} to "$displayName"');

      // Refresh the state from Firestore.
      await _updateStateWithUserData(firebaseUser.uid,
          displayNameIfNotSet: displayName);
      dev.log(
          'Auth (updateDisplayName): Process completed for ${firebaseUser.uid}');
    } catch (e) {
      dev.log('Auth (updateDisplayName): Error for ${firebaseUser.uid}: $e');
      if (!mounted) return;
      // Attempt to refresh to a consistent state on error
      await _updateStateWithUserData(firebaseUser.uid);
    }
  }

  Future<void> updateUserPhotoUrl(String photoUrl) async {
    if (!mounted) return;
    state = const AsyncValue.loading();

    final firebaseUser = _authService.currentUser;
    if (firebaseUser == null) {
      if (!mounted) return;
      state = AsyncValue.error(
          Exception('User not authenticated for photo update'),
          StackTrace.current);
      return;
    }
    dev.log('Auth (updateUserPhotoUrl): Attempting for ${firebaseUser.uid}');

    try {
      await firebaseUser.updatePhotoURL(photoUrl);
      dev.log(
          'Auth (updateUserPhotoUrl): Firebase Auth photoURL updated for ${firebaseUser.uid}');

      await _firestore.collection('users').doc(firebaseUser.uid).update(
          {'photoUrl': photoUrl, 'updatedAt': FieldValue.serverTimestamp()});
      dev.log(
          'Auth (updateUserPhotoUrl): Firestore photoUrl directly updated for ${firebaseUser.uid}');

      await _updateStateWithUserData(firebaseUser.uid);
      dev.log(
          'Auth (updateUserPhotoUrl): Process completed for ${firebaseUser.uid}');
    } catch (e) {
      dev.log('Auth (updateUserPhotoUrl): Error for ${firebaseUser.uid}: $e');
      if (!mounted) return;
      await _updateStateWithUserData(firebaseUser.uid);
    }
  }

  Future<void> forceRefreshUserData(String userId) async {
    if (!mounted) {
      dev.log(
          'Auth (forceRefreshUserData): Not mounted for $userId. Aborting.');
      return;
    }

    dev.log(
        'Auth (forceRefreshUserData): Force refreshing user data for $userId');

    // Get the latest data from Firestore
    try {
      // Ensure we get a fresh copy directly from the server, not from cache
      final userDocRef = _firestore.collection('users').doc(userId);
      final userDoc = await userDocRef.get(GetOptions(source: Source.server));

      if (!userDoc.exists) {
        dev.log(
            'Auth (forceRefreshUserData): User document not found for $userId');
        return;
      }

      final data = userDoc.data();
      dev.log(
          'Auth (forceRefreshUserData): User document retrieved, fields: ${data?.keys.join(', ')}');

      // Log license data directly from the document for debugging
      if (data != null && data.containsKey('licenseExpiryDate')) {
        final licenseRawData = data['licenseExpiryDate'];
        final licenseType = licenseRawData.runtimeType;
        dev.log(
            'Auth (forceRefreshUserData): Found licenseExpiryDate in document with type: $licenseType, value: $licenseRawData');

        // Additional logging for Timestamp type to help debug conversion issues
        if (licenseRawData is Timestamp) {
          dev.log(
              'Auth (forceRefreshUserData): License expiry as Timestamp.toDate(): ${licenseRawData.toDate()}');
          dev.log(
              'Auth (forceRefreshUserData): License expiry as milliseconds: ${licenseRawData.toDate().millisecondsSinceEpoch}');
        } else if (licenseRawData is int) {
          dev.log(
              'Auth (forceRefreshUserData): License expiry as DateTime from milliseconds: ${DateTime.fromMillisecondsSinceEpoch(licenseRawData)}');
        }
      } else {
        dev.log(
            'Auth (forceRefreshUserData): No licenseExpiryDate found in document');
      }

      // Parse the user document with better error handling
      UserModel? userModel;
      try {
        userModel = UserModel.fromFirestore(userDoc);
        dev.log(
            'Auth (forceRefreshUserData): UserModel successfully created from Firestore document');

        // Log license date info for debugging
        if (userModel.licenseExpiryDate != null) {
          dev.log(
              'Auth (forceRefreshUserData): License expiry date parsed as: ${userModel.licenseExpiryDate} (${userModel.licenseExpiryDate!.millisecondsSinceEpoch})');
        } else {
          dev.log(
              'Auth (forceRefreshUserData): No license expiry date parsed in UserModel');
        }
      } catch (parseError) {
        dev.log(
            'Auth (forceRefreshUserData): Error parsing user document: $parseError');
        // Try an alternative approach if we have a specific error
        if (parseError
                .toString()
                .contains('type \'int\' is not a subtype of type \'String\'') ||
            parseError.toString().contains('Timestamp') ||
            parseError.toString().contains('DateTime')) {
          dev.log(
              'Auth (forceRefreshUserData): Trying alternative parsing for known issue with date conversion');

          // Get raw data to handle manually
          final data = userDoc.data();
          if (data != null) {
            // Handle the licenseExpiryDate specifically
            DateTime? licenseDate;
            try {
              if (data['licenseExpiryDate'] is int) {
                licenseDate = DateTime.fromMillisecondsSinceEpoch(
                    data['licenseExpiryDate'] as int);
                dev.log(
                    'Auth (forceRefreshUserData): Successfully converted integer timestamp to DateTime: $licenseDate');
              } else if (data['licenseExpiryDate'] is Timestamp) {
                licenseDate = (data['licenseExpiryDate'] as Timestamp).toDate();
                dev.log(
                    'Auth (forceRefreshUserData): Successfully converted Timestamp to DateTime: $licenseDate');
              } else if (data['licenseExpiryDate'] is String) {
                licenseDate =
                    DateTime.parse(data['licenseExpiryDate'] as String);
                dev.log(
                    'Auth (forceRefreshUserData): Successfully parsed date string to DateTime: $licenseDate');
              }
            } catch (dateError) {
              dev.log(
                  'Auth (forceRefreshUserData): Error parsing license date: $dateError');
              licenseDate = null;
            }

            // Try to construct the user model manually with the fixed date
            try {
              userModel = UserModel(
                id: userDoc.id,
                displayName: (data['displayName'] as String?) ?? '',
                email: (data['email'] as String?) ?? '',
                photoUrl: data['photoUrl'] as String?,
                licenseExpiryDate: licenseDate,
                licenseExpiryNotificationEnabled:
                    data['licenseExpiryNotificationEnabled'] as bool?,
                isEmailVerified: data['isEmailVerified'] as bool? ?? false,
                providerId: data['providerId'] as String?,
                providerData: (data['providerData'] as List<dynamic>?)
                    ?.map((e) => e as Map<String, dynamic>)
                    .toList(),
              );
              dev.log(
                  'Auth (forceRefreshUserData): Successfully created UserModel using alternative approach');

              // Log the result of our manual construction
              if (licenseDate != null) {
                dev.log(
                    'Auth (forceRefreshUserData): Manually constructed with license date: $licenseDate');
              } else {
                dev.log(
                    'Auth (forceRefreshUserData): Manually constructed with no license date');
              }
            } catch (manualConstructError) {
              dev.log(
                  'Auth (forceRefreshUserData): Failed to manually construct UserModel: $manualConstructError');
              rethrow; // Let the outer catch handle this
            }
          }
        } else {
          // Re-throw unknown errors
          rethrow;
        }
      }

      if (userModel == null) {
        dev.log('Auth (forceRefreshUserData): Failed to create UserModel');
        return;
      }

      if (!mounted) return;

      // Update the state
      state = AsyncValue.data(userModel);
      dev.log(
          'Auth (forceRefreshUserData): State updated for $userId with latest Firestore data');
    } catch (e) {
      dev.log(
          'Auth (forceRefreshUserData): Error refreshing user data for $userId: $e');
      if (!mounted) return;
      // Don't update state to error, just log the error
    }
  }

  @override
  void dispose() {
    dev.log('AuthNotifier disposing. Mounted: $mounted');
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

// Assuming UserModel has a constructor like:
// UserModel({required this.id, required this.email, this.displayName, this.photoUrl, this.isEmailVerified = false, ...});

// And a factory fromFirestore:
// factory UserModel.fromFirestore(DocumentSnapshot doc) {
//   final data = doc.data() as Map<String, dynamic>;
//   return UserModel(
//     id: doc.id,
//     email: data['email'] ?? '',
//     displayName: data['displayName'] ?? '',
//     photoUrl: data['photoUrl'],
//     isEmailVerified: data['isEmailVerified'] ?? false,
//   );
// }

// And UserModel has a copyWith method:
// UserModel copyWith({
//   String? id,
//   String? email,
//   String? displayName,
//   String? photoUrl,
//   bool? isEmailVerified,
// }) {
//   return UserModel(
//     id: id ?? this.id,
//     email: email ?? this.email,
//     displayName: displayName ?? this.displayName,
//     photoUrl: photoUrl ?? this.photoUrl,
//     isEmailVerified: isEmailVerified ?? this.isEmailVerified,
//   );
// }
