import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../providers/auth_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as dev;
import 'package:flutter/services.dart';

class EmailVerificationScreen extends ConsumerStatefulWidget {
  const EmailVerificationScreen({super.key});

  @override
  ConsumerState<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends ConsumerState<EmailVerificationScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  bool _isResendEnabled = true;
  int _resendCountdown = 60;
  Timer? _resendTimer;
  Timer? _checkVerificationTimer;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    // Initialize animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController, 
        curve: Curves.easeInOut,
      ),
    );
    
    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController, 
        curve: Curves.easeOutQuad,
      ),
    );
    
    _animationController.forward();
    
    final user = FirebaseAuth.instance.currentUser;
    
    // Check if user is a Google user by checking provider data
    final isGoogleUser = user?.providerData.any((p) => p.providerId == 'google.com') ?? false;
    
    if (isGoogleUser) {
      // Google users are already verified, skip this screen
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) context.go('/dashboard');
      });
      return;
    }
    
    // Also check if user is already verified (for email users who verified already)
    if (user?.emailVerified == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) context.go('/dashboard');
      });
      return;
    }
    
    // Start checking for verification status for unverified email users
    _startVerificationCheck();
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    _checkVerificationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startVerificationCheck() {
    // Check every 5 seconds if the email has been verified
    _checkVerificationTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      try {
        // Use the auth provider to check verification status
        final isVerified = await ref.read(authProvider.notifier).checkEmailVerified();

        if (isVerified) {
          dev.log('Email verified during periodic check! Navigating to dashboard');
          _checkVerificationTimer?.cancel();

          if (mounted) {
            // Navigate to dashboard
            context.go('/dashboard');
          }
        } else {
          // Try alternative method as fallback
          try {
            final alternativeVerified = await _checkVerificationWithoutReload();

            if (alternativeVerified) {
              dev.log('Email verified via alternative check during periodic check! Navigating to dashboard');
              _checkVerificationTimer?.cancel();

              if (mounted) {
                // Navigate to dashboard
                context.go('/dashboard');
              }
            }
          } catch (alternativeError) {
            // Just log the error and continue checking
            dev.log('Alternative verification check failed during periodic check: $alternativeError');
          }
        }
      } catch (e) {
        dev.log('Error during periodic verification check: $e');
      }
    });
  }

  Future<void> _resendVerificationEmail() async {
    if (!_isResendEnabled) return;

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await ref.read(authProvider.notifier).sendEmailVerification();
      
      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).emailVerificationSent),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      
      // Start countdown for resend button
      setState(() {
        _isResendEnabled = false;
        _resendCountdown = 60;
      });
      _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          if (_resendCountdown > 0) {
            _resendCountdown--;
          } else {
            _isResendEnabled = true;
            timer.cancel();
          }
        });
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = S.of(context).emailVerificationFailed;
        });
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).emailVerificationFailed),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      dev.log('Error sending verification email: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshVerificationStatus() async {
    // Provide haptic feedback
    HapticFeedback.mediumImpact();
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Use the auth provider to check verification status
      final isVerified = await ref.read(authProvider.notifier).checkEmailVerified();

      if (isVerified) {
        dev.log('Email verified! Navigating to dashboard');
        if (mounted) {
          // Navigate to dashboard
          context.go('/dashboard');
        }
      } else {
        // Try our alternative method as a fallback
        try {
          final alternativeVerified = await _checkVerificationWithoutReload();

          if (alternativeVerified) {
            dev.log('Email verified via alternative check! Navigating to dashboard');
            if (mounted) {
              // Navigate to dashboard
              context.go('/dashboard');
            }
            return;
          }
        } catch (alternativeError) {
          dev.log('Alternative verification check failed: $alternativeError');
        }

        if (mounted) {
          setState(() {
            // Using a direct string until localization is updated
            _errorMessage = 'Email not verified yet. Please check your inbox.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = S.of(context).emailVerificationFailed;
        });
      }
      dev.log('Error checking verification status: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Alternative method to check verification without using reload()
  Future<bool> _checkVerificationWithoutReload() async {
    try {
      // Get a fresh instance of the current user
      final auth = FirebaseAuth.instance;

      // Sign out and sign back in to refresh the user state
      final email = auth.currentUser?.email;
      final uid = auth.currentUser?.uid;

      if (email == null || uid == null) {
        dev.log('Cannot check verification: missing email or uid');
        return false;
      }

      // Check with Firestore if possible
      try {
        final firestore = FirebaseFirestore.instance;
        final userDoc = await firestore.collection('users').doc(uid).get();

        if (userDoc.exists) {
          final isVerified = userDoc.data()?['isEmailVerified'] == true;
          dev.log('Firestore verification status: $isVerified');
          return isVerified;
        }
      } catch (firestoreError) {
        dev.log('Firestore check failed: $firestoreError');
        // Continue with Firebase Auth check
      }

      // Get user info directly from Firebase Auth
      try {
        User? currentUser = auth.currentUser;
        if (currentUser == null) {
          dev.log('Alternative check (initial): No current user found.');
          return false;
        }
        dev.log('Alternative check: Attempting token refresh and reload for ${currentUser.uid}');
        await currentUser.getIdToken(true);
        await currentUser.reload();
        
        // After reload, ensure we have the latest instance from auth
        currentUser = auth.currentUser;
        if (currentUser == null) {
          dev.log('Alternative check: User became null after reload.');
          return false;
        }
        dev.log('Alternative check (after reload): Firebase Auth emailVerified status: ${currentUser.emailVerified}');
        return currentUser.emailVerified == true;

      } catch (authError) {
        dev.log('Firebase Auth verification check failed during reload/token refresh: $authError');
        if (authError.toString().contains('PigeonUserInfo') || authError.toString().contains('List<Object?>')) {
          dev.log('Alternative check: Caught cast error. Trying to get a fresh user instance and check directly.');
          final freshUserInstance = FirebaseAuth.instance.currentUser;
          if (freshUserInstance != null) {
            // Optionally, try to reload this fresh instance too, but be cautious
            try {
              await freshUserInstance.reload(); // This might also throw, but worth a try
              dev.log('Alternative check (fresh instance reloaded): emailVerified: ${freshUserInstance.emailVerified}');
              return freshUserInstance.emailVerified == true;
            } catch (freshReloadError) {
              dev.log('Alternative check (fresh instance reload failed): $freshReloadError. Checking emailVerified on non-reloaded fresh instance.');
              dev.log('Alternative check (fresh instance, no reload): emailVerified: ${freshUserInstance.emailVerified}');
              return freshUserInstance.emailVerified == true;
            }
          } else {
            dev.log('Alternative check: Fresh user instance is null after cast error.');
            return false;
          }
        }
        return false; // Fallback for other authErrors
      }
    } catch (e) {
      dev.log('Alternative verification check error: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final user = FirebaseAuth.instance.currentUser;
    final email = user?.email ?? '';

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.emailVerification,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.accentColor),
          onPressed: () {
            // Sign out and go back to login screen
            ref.read(authProvider.notifier).signOut();
            context.go('/login');
          },
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.2),
            ],
            stops: const [0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: child,
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Email verification animation/icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.mark_email_unread_rounded,
                      size: 70,
                      color: context.accentColor,
                    ),
                  ),
                  const SizedBox(height: 32),
                  
                  // Title and instructions
                  Text(
                    l10n.pleaseVerifyEmail,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: context.primaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  
                  // Email information card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: context.accentColor.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    color: context.cardColor.withOpacity(0.8),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Text(
                            // Using a direct string until localization is updated
                            'We have sent a verification email to:',
                            style: TextStyle(
                              fontSize: 16,
                              color: context.secondaryTextColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          SelectableText(
                            email,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: context.accentColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Please check your inbox and click the verification link to continue.',
                            style: TextStyle(
                              fontSize: 14,
                              color: context.secondaryTextColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red.shade300),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red.shade300),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red.shade300),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  
                  const Spacer(),
                  
                  // Main action button
                  ElevatedButton(
                    onPressed: _isLoading ? null : _refreshVerificationStatus,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
                      minimumSize: const Size(double.infinity, 56),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(14),
                      ),
                      elevation: 3,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: _isLoading
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                // Using a direct string until localization is updated
                                'Checking verification status...',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            // Using a direct string until localization is updated
                            'I\'ve Verified My Email',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Resend button
                  OutlinedButton(
                    onPressed: _isLoading || !_isResendEnabled ? null : _resendVerificationEmail,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: context.accentColor,
                      minimumSize: const Size(double.infinity, 56),
                      side: BorderSide(color: context.accentColor, width: 2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(14),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      _isResendEnabled
                          ? l10n.resendVerificationEmail
                          : 'Resend in $_resendCountdown seconds',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
