import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/form_validators.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/providers/auth_providers.dart';
import '../../../../core/services/auth_service.dart';
import 'dart:developer' as dev;
// Hide duplicate provider
import '../../../../core/theme/theme_extensions.dart';
import '../../../../features/ads/application/interstitial_ad_service.dart';
import '../../../../features/ads/presentation/managers/app_open_ad_manager.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  String? _errorMessage;
  bool _isGoogleSigningIn = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _errorMessage = null);

    try {
      final authService = ref.read(authServiceProvider);
      dev.log('LoginScreen: Attempting to sign in with email: ${_emailController.text.trim()}');
      
      final result = await authService.signInWithEmail(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (!mounted) return;

      if (result.success) {
        // Enable app open ads after successful login
        final adManager = ref.read(appOpenAdManagerProvider.notifier);
        adManager.showAdsAfterLogin = true;
        dev.log('LoginScreen: Enabled ads after successful login');
        
        // Also preload interstitial ads after login
        try {
          final interstitialAdService = ref.read(interstitialAdServiceProvider.notifier);
          // Use Future.microtask to avoid blocking UI
          Future.microtask(() {
            interstitialAdService.preloadAd();
            dev.log('LoginScreen: Triggered interstitial ad preload');
          });
        } catch (e) {
          dev.log('LoginScreen: Error preloading interstitial ads: $e');
        }
        
        // Show feedback to the user
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sign-in successful'),
            duration: Duration(seconds: 2),
          ),
        );
        
        // Navigate to dashboard on success
        dev.log('LoginScreen: Sign in successful, navigating to dashboard');
        context.go('/dashboard');
      } else {
        // Display error message
        _handleAuthError('Email sign-in failed', result.errorMessage, null);
      }
    } catch (e) {
      if (!mounted) return;
      _handleAuthError('Unexpected error during login', 'An unexpected error occurred. Please try again.', e);
    }
  }

  Future<void> _handleGoogleSignIn() async {
    final authService = ref.read(authServiceProvider);
    final authProcess = ref.watch(authProcessProvider);
    
    // Don't start if already in process
    if (authProcess != AuthProcess.idle || _isGoogleSigningIn) return;
    
    setState(() {
      _errorMessage = null;
      _isGoogleSigningIn = true; // Set local loading flag immediately
    });
    
    // Show immediate feedback to the user
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connecting to Google...'),
        duration: Duration(seconds: 2),
      ),
    );
    
    try {
      dev.log('LoginScreen: Starting Google Sign-In');
      
      final result = await authService.signInWithGoogle();
      
      if (!mounted) return;
      
      if (result.success) {
        // Show loading indicator for the ad initialization
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sign-in successful. Loading app...'),
            duration: Duration(seconds: 2),
          ),
        );
        
        // Enable app open ads after successful login
        final adManager = ref.read(appOpenAdManagerProvider.notifier);
        adManager.showAdsAfterLogin = true;
        dev.log('LoginScreen: Enabled ads after successful Google login');
        
        // Also preload interstitial ads after login
        try {
          final interstitialAdService = ref.read(interstitialAdServiceProvider.notifier);
          // Load ads in background to avoid slowing down the UI transition
          Future.microtask(() {
            interstitialAdService.preloadAd();
            dev.log('LoginScreen: Triggered interstitial ad preload after Google login');
          });
        } catch (e) {
          dev.log('LoginScreen: Error preloading interstitial ads: $e');
        }
        
        // Navigate to dashboard on success
        dev.log('LoginScreen: Google Sign-In successful, navigating to dashboard');
        context.go('/dashboard');
        // Keep the loading state active as we navigate away
      } else {
        // Display error message and reset loading state
        _handleAuthError('Google Sign-In failed', result.errorMessage, null);
        setState(() => _isGoogleSigningIn = false);
      }
    } catch (e) {
      if (!mounted) return;
      _handleAuthError('Unexpected error during Google Sign-In', 'An unexpected error occurred. Please try again.', e);
      setState(() => _isGoogleSigningIn = false);
    }
  }
  
  void _handleAuthError(String logMessage, String? userMessage, Object? exception) {
    // Log the error for debugging
    if (exception != null) {
      dev.log('LoginScreen ERROR: $logMessage', error: exception);
    } else {
      dev.log('LoginScreen ERROR: $logMessage - $userMessage');
    }
    
    // Show error to user
    setState(() => _errorMessage = userMessage ?? 'An unexpected error occurred');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final authProcess = ref.watch(authProcessProvider);
    final isLoading = authProcess != AuthProcess.idle || _isGoogleSigningIn;

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: IntrinsicHeight(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const SizedBox(height: 40),
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              height: 100,
                              width: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: context.accentColor,
                                  width: 3,
                                ),
                              ),
                              child: Icon(
                                Icons.local_gas_station,
                                size: 50,
                                color: context.accentColor,
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                          Text(
                            l10n.welcomeBack,
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: context.accentColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          
                          // Error message if present
                          if (_errorMessage != null)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.red.shade300),
                              ),
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade300),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          if (_errorMessage != null)
                            const SizedBox(height: 24),
                            
                          // Form
                          Expanded(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  TextFormField(
                                    controller: _emailController,
                                    style: TextStyle(color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.email,
                                      labelStyle: TextStyle(color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.email, color: context.accentColor),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.secondaryAccentColor.withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.accentColor),
                                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType: TextInputType.emailAddress,
                                    textInputAction: TextInputAction.next,
                                    validator: FormValidators.validateEmail,
                                  ),
                                  const SizedBox(height: 16),
                                  TextFormField(
                                    controller: _passwordController,
                                    style: TextStyle(color: context.primaryTextColor),
                                    enabled: !isLoading,
                                    decoration: InputDecoration(
                                      labelText: l10n.password,
                                      labelStyle: TextStyle(color: context.secondaryTextColor),
                                      prefixIcon: Icon(Icons.lock, color: context.accentColor),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          _obscurePassword ? Icons.visibility : Icons.visibility_off,
                                          color: context.secondaryTextColor,
                                        ),
                                        onPressed: () {
                                          setState(() => _obscurePassword = !_obscurePassword);
                                        },
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.secondaryAccentColor.withOpacity(0.3)),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: context.accentColor),
                                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(color: Colors.red.shade300),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    obscureText: _obscurePassword,
                                    validator: FormValidators.validatePassword,
                                  ),
                                  
                                  // Rest of form content below
                                  Spacer(flex: 1),
                                  
                                  // Buttons and sign in section
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton(
                                      onPressed: isLoading ? null : () => context.push('/forgot-password'),
                                      style: TextButton.styleFrom(
                                        foregroundColor: context.accentColor,
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      ),
                                      child: Text(
                                        l10n.forgotPassword,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  ElevatedButton(
                                    onPressed: isLoading ? null : _handleLogin,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: context.accentColor,
                                      foregroundColor: context.isDarkMode ? Colors.black : Colors.white,
                                      minimumSize: const Size(double.infinity, 56),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 4,
                                    ),
                                    child: isLoading && authProcess == AuthProcess.emailSignIn
                                        ? SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(context.isDarkMode ? Colors.black : Colors.white),
                                            ),
                                          )
                                        : Text(
                                            l10n.login,
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Divider(
                                          color: context.secondaryTextColor,
                                          thickness: 1,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 16),
                                        child: Text(
                                          l10n.orContinueWith,
                                          style: TextStyle(
                                            color: context.secondaryTextColor,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Divider(
                                          color: context.secondaryTextColor,
                                          thickness: 1,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  // Use our optimized Google sign-in button
                                  _buildGoogleSignInButton(),
                                  const SizedBox(height: 24),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        l10n.dontHaveAccount,
                                        style: TextStyle(
                                          color: context.secondaryTextColor,
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: isLoading ? null : () => context.push('/signup'),
                                        style: TextButton.styleFrom(
                                          foregroundColor: context.accentColor,
                                        ),
                                        child: Text(l10n.signup),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // Update UI for the login button
  Widget _buildGoogleSignInButton() {
    final isLoading = ref.watch(authProcessProvider) == AuthProcess.googleSignIn || 
                      _isGoogleSigningIn;
    
    return ElevatedButton.icon(
      onPressed: isLoading ? null : _handleGoogleSignIn,
      icon: isLoading 
          ? Container(
              width: 20,
              height: 20,
              padding: const EdgeInsets.all(2.0),
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Image.asset(
              'assets/images/google_logo.png',
              height: 20.0,
              width: 20.0,
            ),
      label: Text(isLoading ? 'Signing in...' : 'Sign in with Google'),
      style: ElevatedButton.styleFrom(
        backgroundColor: isLoading ? Colors.grey : Colors.white,
        foregroundColor: isLoading ? Colors.white60 : Colors.black87,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
} 