import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'generated/app_localizations.dart';
import 'core/providers/locale_provider.dart';
import 'core/providers/theme_provider.dart';
import 'app/routes/app_router.dart';
import 'core/theme/app_colors.dart';
import 'shared/widgets/offline_banner.dart';
// Remove the alias for core_auth if it's being replaced, or ensure distinct usage.
// For now, assuming features/auth/providers/auth_provider.dart is the primary one.
// import 'core/providers/auth_providers.dart' as core_auth;
// This might be for the AuthService class itself, not the provider
import 'dart:developer' as dev;
import 'features/ads/presentation/managers/app_open_ad_manager.dart';
// Ensure this is the provider we want for AuthNotifier's state
import 'features/auth/providers/auth_provider.dart' show authProvider;

// Global navigator key for error handling
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// Custom Provider for Dark Theme
final darkThemeProvider = Provider.family<ThemeData, Locale>((ref, locale) {
  final isArabic = locale.languageCode == 'ar';

  final textTheme = isArabic
      ? GoogleFonts.tajawalTextTheme()
      : GoogleFonts.robotoTextTheme();

  return ThemeData.dark().copyWith(
    applyElevationOverlayColor: false,
    textTheme: textTheme.copyWith(
      titleLarge: textTheme.titleLarge?.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      titleMedium: textTheme.titleMedium?.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: textTheme.bodyLarge?.copyWith(
        fontSize: 16,
      ),
      bodyMedium: textTheme.bodyMedium?.copyWith(
        fontSize: 14,
      ),
    ).apply(bodyColor: Colors.white),
    colorScheme: const ColorScheme.dark(
      primary: AppColors.gold,
      secondary: AppColors.burgundy,
      surface: AppColors.darkGray,
      error: Colors.red,
      surfaceTint: Colors.transparent,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.darkGray,
      elevation: 0,
      iconTheme: IconThemeData(color: AppColors.gold),
      titleTextStyle: TextStyle(
        color: AppColors.gold,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
    cardTheme: const CardTheme(
      color: AppColors.darkCardColor, // Use our dedicated dark card color for better contrast
      shadowColor: Colors.black26,
      elevation: 4,
      surfaceTintColor: Colors.transparent,
    ),
    dialogTheme: const DialogTheme(
      backgroundColor: AppColors.darkCardColor,
      surfaceTintColor: Colors.transparent,
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: AppColors.darkCardColor,
      surfaceTintColor: Colors.transparent,
    ),
    popupMenuTheme: const PopupMenuThemeData(
      color: AppColors.darkCardColor,
      surfaceTintColor: Colors.transparent,
    ),
    scaffoldBackgroundColor: AppColors.darkGray,
    dividerColor: AppColors.gold.withOpacity(0.3),
    iconTheme: const IconThemeData(
      color: AppColors.gold,
    ),
    buttonTheme: const ButtonThemeData(
      buttonColor: AppColors.gold,
      textTheme: ButtonTextTheme.primary,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.gold,
        foregroundColor: Colors.black,
        elevation: 4,
        surfaceTintColor: Colors.transparent,
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        surfaceTintColor: Colors.transparent,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.gold,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderSide: const BorderSide(color: AppColors.gold),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: AppColors.gold, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      labelStyle: const TextStyle(color: Colors.white70),
    ),
  );
});

/// Custom Provider for Light Theme
final lightThemeProvider = Provider.family<ThemeData, Locale>((ref, locale) {
  final isArabic = locale.languageCode == 'ar';

  final textTheme = isArabic
      ? GoogleFonts.tajawalTextTheme()
      : GoogleFonts.robotoTextTheme();

  return ThemeData.light().copyWith(
    applyElevationOverlayColor: false,
    textTheme: textTheme.copyWith(
      titleLarge: textTheme.titleLarge?.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      titleMedium: textTheme.titleMedium?.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: textTheme.bodyLarge?.copyWith(
        fontSize: 16,
      ),
      bodyMedium: textTheme.bodyMedium?.copyWith(
        fontSize: 14,
      ),
    ),
    colorScheme: ColorScheme.light(
      primary: AppColors.burgundy,
      secondary: AppColors.gold,
      surface: Colors.white,
      error: Colors.red,
      surfaceTint: Colors.transparent,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      elevation: 0,
      iconTheme: const IconThemeData(color: AppColors.burgundy),
      titleTextStyle: const TextStyle(
        color: AppColors.burgundy,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      shadowColor: Colors.grey.shade300,
      elevation: 2,
      surfaceTintColor: Colors.transparent,
    ),
    dialogTheme: const DialogTheme(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    popupMenuTheme: const PopupMenuThemeData(
      surfaceTintColor: Colors.transparent,
    ),
    bannerTheme: const MaterialBannerThemeData(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
    ),
    scaffoldBackgroundColor: Colors.grey.shade100,
    dividerColor: AppColors.gold.withOpacity(0.3),
    iconTheme: const IconThemeData(
      color: AppColors.burgundy,
    ),
    buttonTheme: const ButtonThemeData(
      buttonColor: AppColors.burgundy,
      textTheme: ButtonTextTheme.primary,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.burgundy,
        foregroundColor: Colors.white,
        elevation: 2,
        surfaceTintColor: Colors.transparent,
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        surfaceTintColor: Colors.transparent,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.burgundy,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: AppColors.burgundy, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      labelStyle: TextStyle(color: Colors.grey.shade700),
    ),
  );
});

class App extends ConsumerWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize and listen to auth state changes
    // Watch the new authProvider for UserModel state
    final authValue = ref.watch(authProvider);
    // Assuming authProcessProvider is still relevant or needs to be adapted
    // For now, let's comment out authProcess if it's tied to the old system
    // final authProcess = ref.watch(core_auth.authProcessProvider);

    // Log the auth state for debugging
    authValue.when(
      data: (userModel) => dev.log('App: Current auth state (UserModel): ${userModel?.id}, Verified: ${userModel?.isEmailVerified}'),
      loading: () => dev.log('App: Auth state loading...'),
      error: (err, stack) => dev.log('App: Auth state error: $err'),
    );
    // dev.log('App: Current auth state: $authState, auth process: $authProcess');

    final localeState = ref.watch(localeProvider);
    final router = ref.watch(routerProvider);
    final analytics = FirebaseAnalytics.instance;

    // Watch the theme mode
    final themeMode = ref.watch(themeModeProvider);

    // Only load the initial App Open Ad after login and do it safely
    // after the build is complete using post-frame callback
    // Check based on the new authProvider's data
    if (authValue.asData?.value?.id != null && authValue.asData!.value!.isEmailVerified) {
      // Avoid modifying providers during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          // This will run after the current build cycle is complete
          ref.read(appOpenAdManagerProvider.notifier).loadAd();
        } catch (e) {
          // Silently handle any errors that might occur during ad loading
          // to prevent app crashes
          dev.log('Error loading initial app open ad: $e');
        }
      });
    }

    return _AppLifecycleReactor(
      child: MaterialApp.router(
        title: 'Oil Plus',
        debugShowCheckedModeBanner: false,
        routerConfig: router,
        locale: localeState,
        localizationsDelegates: const [
          S.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: S.supportedLocales,

        // Use theme mode from provider
        themeMode: themeMode,

        // Light theme
        theme: ref.watch(lightThemeProvider(localeState)),

        // Dark theme
        darkTheme: ref.watch(darkThemeProvider(localeState)),

        scaffoldMessengerKey: GlobalKey<ScaffoldMessengerState>(),
        builder: (context, child) {
          return Stack(
            children: [
              // Main content with SafeArea to ensure it's below the status bar
              SafeArea(
                child: child ?? const SizedBox.shrink(),
              ),

              // Offline banner that sits at the top of the screen
              const Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: OfflineBanner(),
              ),

              // Loading overlay for auth process
              // if (authProcess != AuthProcess.idle) // Commenting out if authProcessProvider is removed/changed
              //   Positioned.fill(
              //     child: Container(
              //       color: Colors.black54,
              //       child: Center(
              //         child: Column(
              //           mainAxisSize: MainAxisSize.min,
              //           children: [
              //             CircularProgressIndicator(
              //               valueColor: AlwaysStoppedAnimation<Color>(
              //                 Theme.of(context).brightness == Brightness.dark
              //                   ? AppColors.gold
              //                   : AppColors.burgundy
              //               ),
              //             ),
              //             const SizedBox(height: 16),
              //             Text(
              //               'Processing authentication...', // Generic message
              //               // 'Processing ${_getAuthProcessText(authProcess)}...',
              //               style: const TextStyle(
              //                 color: Colors.white,
              //                 fontSize: 16,
              //               ),
              //             ),
              //           ],
              //         ),
              //       ),
              //     ),
              //   ),
            ],
          );
        },
      ),
    );
  }

  // String _getAuthProcessText(AuthProcess process) { // Commenting out if authProcessProvider is removed/changed
  //   switch (process) {
  //     case AuthProcess.emailSignIn:
  //       return 'sign in';
  //     case AuthProcess.emailSignUp:
  //       return 'sign up';
  //     case AuthProcess.googleSignIn:
  //       return 'Google sign in';
  //     case AuthProcess.signOut:
  //       return 'sign out';
  //     case AuthProcess.passwordReset:
  //       return 'password reset';
  //     case AuthProcess.profileUpdate:
  //       return 'profile update';
  //     default:
  //       return 'authentication';
  //   }
  // }
}

/// A widget that listens to app lifecycle state changes and shows App Open Ads.
class _AppLifecycleReactor extends ConsumerStatefulWidget {
  final Widget child;
  const _AppLifecycleReactor({required this.child});

  @override
  ConsumerState<_AppLifecycleReactor> createState() => _AppLifecycleReactorState();
}

class _AppLifecycleReactorState extends ConsumerState<_AppLifecycleReactor>
    with WidgetsBindingObserver {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Dispose ad manager resources if needed, but it's keepAlive=true
    // ref.read(appOpenAdManagerProvider.notifier).disposeAd();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      dev.log('AppLifecycle: Resumed');
      // App Open Ads are now managed by appOpenAdManager directly
      // which will only show ads if showAdsAfterLogin is true

      // Refresh user data including license expiry date when app is resumed
      _refreshUserData();
    }
     if (state == AppLifecycleState.paused) {
       dev.log('AppLifecycle: Paused');
       // Let the AppOpenAdManager handle ad loading
       // ref.read(appOpenAdManagerProvider.notifier).loadAd();
     }
  }

  // Refresh user data from Firestore to ensure license expiry date is up-to-date
  Future<void> _refreshUserData() async {
    try {
      // Use the new authProvider
      final userModelAsyncValue = ref.read(authProvider);
      final userId = userModelAsyncValue.asData?.value?.id;

      if (userId != null) {
        dev.log('AppLifecycle: Refreshing user data including license expiry date for $userId');
        await ref.read(authProvider.notifier).forceRefreshUserData(userId);
        dev.log('AppLifecycle: User data refresh completed');
      }
    } catch (e) {
      dev.log('AppLifecycle: Error refreshing user data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}