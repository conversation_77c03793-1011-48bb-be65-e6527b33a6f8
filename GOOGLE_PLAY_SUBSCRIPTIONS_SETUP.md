# Google Play Console Subscriptions Setup Guide

## Overview
This guide will walk you through setting up in-app subscriptions for the Oil Change Tracker app in Google Play Console. The app currently supports Premium and Family subscription tiers with both monthly and yearly billing options.

## Prerequisites
- Google Play Console Developer Account
- App uploaded to Google Play Console (Internal Testing or higher)
- Signed app bundle (`app-release.aab` ready for upload)

## Step 1: Upload Your App Bundle

1. **Navigate to Google Play Console**
   - Go to [Google Play Console](https://play.google.com/console)
   - Select your app or create a new app

2. **Upload the App Bundle**
   - Navigate to **Release** → **Production** (or Testing track)
   - Click **Create new release**
   - Upload the `app-release.aab` file from `build/app/outputs/bundle/release/`
   - Add release notes and complete the release setup

## Step 2: Set Up Subscriptions

### 2.1 Enable In-App Products
1. Go to **Monetize** → **Products** → **Subscriptions**
2. If not already enabled, click **Set up a merchant account** and complete Google Play billing setup

### 2.2 Create Subscription Products

Create the following 4 subscription products based on our app's subscription tiers:

#### Premium Monthly Subscription
- **Product ID**: `premium_monthly`
- **Name**: Premium Monthly Plan
- **Description**: Premium features with monthly billing
- **Billing period**: 1 Month
- **Price**: $2.99 USD (adjust for your market)
- **Free trial**: 7 days (optional)
- **Grace period**: 3 days (recommended)

#### Premium Yearly Subscription
- **Product ID**: `premium_yearly`
- **Name**: Premium Yearly Plan
- **Description**: Premium features with yearly billing (30% savings)
- **Billing period**: 1 Year
- **Price**: $24.99 USD (30% discount from monthly)
- **Free trial**: 7 days (optional)
- **Grace period**: 3 days (recommended)

#### Family Monthly Subscription
- **Product ID**: `family_monthly`
- **Name**: Family Monthly Plan
- **Description**: Family features with monthly billing
- **Billing period**: 1 Month
- **Price**: $4.99 USD (adjust for your market)
- **Free trial**: 7 days (optional)
- **Grace period**: 3 days (recommended)

#### Family Yearly Subscription
- **Product ID**: `family_yearly`
- **Name**: Family Yearly Plan
- **Description**: Family features with yearly billing (30% savings)
- **Billing period**: 1 Year
- **Price**: $39.99 USD (30% discount from monthly)
- **Free trial**: 7 days (optional)
- **Grace period**: 3 days (recommended)

### 2.3 Configure Subscription Details

For each subscription:

1. **Base Plan Configuration**
   - Set the billing period and price
   - Enable auto-renewal
   - Configure grace periods (recommended: 3 days)
   - Set up account hold (optional)

2. **Offers (Optional)**
   - Create promotional offers (e.g., first month 50% off)
   - Set up free trial periods
   - Configure seasonal promotions

3. **🌍 Localization (English + Arabic Support)**
   - Add localized names and descriptions for your target markets
   - Set region-specific pricing
   - **IMPORTANT**: Since your app supports both English and Arabic, you must provide translations for all subscription products

### Required Product Translations

#### English Descriptions:
- **Premium Monthly:** "Unlock premium features including unlimited vehicles, voice commands, and ad-free experience"
- **Premium Yearly:** "Annual premium subscription with voice commands, unlimited vehicles, and priority support"
- **Family Monthly:** "Share premium features with up to 5 family members including voice commands and analytics"
- **Family Yearly:** "Annual family plan for up to 5 members with all premium features and priority support"

#### Arabic Descriptions (العربية):
- **Premium Monthly:** "اكتشف الميزات المميزة بما في ذلك المركبات غير المحدودة والأوامر الصوتية والتجربة الخالية من الإعلانات"
- **Premium Yearly:** "اشتراك مميز سنوي مع الأوامر الصوتية والمركبات غير المحدودة والدعم الأولوي"
- **Family Monthly:** "شارك الميزات المميزة مع ما يصل إلى 5 أفراد من العائلة بما في ذلك الأوامر الصوتية والتحليلات"
- **Family Yearly:** "خطة عائلية سنوية لما يصل إلى 5 أعضاء مع جميع الميزات المميزة والدعم الأولوي"

#### Arabic Product Names:
- **Premium Monthly:** "الخطة المميزة الشهرية"
- **Premium Yearly:** "الخطة المميزة السنوية" 
- **Family Monthly:** "الخطة العائلية الشهرية"
- **Family Yearly:** "الخطة العائلية السنوية"

### How to Add Arabic Translations in Google Play Console:

1. **For each subscription product:**
   - Go to **Monetize** → **Products** → **Subscriptions**
   - Select your subscription product
   - Click **Store listing**
   - Click **Manage translations**
   - Add **Arabic (العربية)** as a target language
   - Enter the Arabic name and description from the table above

2. **Set up Arabic app store listing:**
   - Go to **Main store listing**
   - Click **Manage translations**
   - Add Arabic translations for your app description, highlighting the subscription features

## Step 3: Testing Subscriptions

### 3.1 Set Up Test Users
1. Go to **Setup** → **License Testing**
2. Add test Gmail accounts under **License testers**
3. Set **Test response** to "RESPOND_NORMALLY"

### 3.2 Create Test Tracks
1. Navigate to **Release** → **Testing** → **Internal testing**
2. Create a release with your app bundle
3. Add your test users to the internal testing track

### 3.3 Test Purchase Flow
1. Install the app from the Play Store on a test device
2. Sign in with a test account
3. Navigate to the subscription screen in the app
4. Test the purchase flow for each subscription tier
5. Verify subscription status in Google Play Console

## Step 4: Real Money Testing

### 3.4 Set Up Closed Testing
1. Create a **Closed Testing** track
2. Upload your app bundle
3. Add a small group of trusted testers
4. Enable real money transactions for thorough testing

## Step 5: Production Release

### 5.1 Review Checklist
- [ ] All subscription products are active
- [ ] Subscription descriptions are clear and accurate
- [ ] Pricing is correctly set for all regions
- [ ] Grace periods and account holds are configured
- [ ] App content rating includes in-app purchases
- [ ] Privacy policy mentions subscription data handling

### 5.2 Release to Production
1. Navigate to **Release** → **Production**
2. Create a new release with your app bundle
3. Complete all required sections:
   - App content
   - Content rating
   - Target audience
   - Data safety
4. Submit for review

## Step 6: Monitor and Optimize

### 6.1 Analytics Setup
Monitor these key metrics in Google Play Console:
- Subscription conversion rates
- Churn rates
- Revenue per user
- Trial to paid conversion

### 6.2 A/B Testing
Consider testing:
- Different trial periods
- Pricing strategies
- Promotional offers
- Subscription descriptions

## Subscription Features in the App

The app currently supports:

### Premium Tier Features
- Voice input for oil change logging
- Advanced analytics and insights
- Cloud backup and sync
- Priority customer support

### Family Tier Features
- All Premium features
- Multiple vehicle management
- Family member accounts
- Shared maintenance schedules
- Enhanced reporting

## Troubleshooting

### Common Issues
1. **"Product not found" errors**: Ensure subscription IDs match exactly in the app code
2. **Purchase verification failures**: Check Google Play Console for proper product setup
3. **Test purchases not working**: Verify test accounts are added to license testing

### Support Resources
- [Google Play Billing Documentation](https://developer.android.com/google/play/billing)
- [Subscription Best Practices](https://developer.android.com/google/play/billing/subscriptions)
- [Play Console Help Center](https://support.google.com/googleplay/android-developer/)

## Next Steps

After setting up subscriptions:

1. **Monitor Performance**: Track key metrics in the first few weeks
2. **User Feedback**: Collect feedback on subscription value and pricing
3. **Iterate**: Adjust pricing or features based on user behavior
4. **Compliance**: Ensure ongoing compliance with Google Play policies

## Important Notes

- Subscription changes can take up to 24 hours to propagate
- Always test thoroughly before production release
- Keep subscription descriptions updated with actual app features
- Consider regional pricing differences
- Monitor for policy changes from Google Play

---

**Ready for Upload**: Your app bundle is built and ready at:
`build/app/outputs/bundle/release/app-release.aab`

Remember to sign your release bundle and test thoroughly before production release! 