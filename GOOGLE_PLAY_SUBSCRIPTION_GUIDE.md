# Google Play Subscription Guide for Oil Change Tracker

## 🚀 App Bundle Information
- **Build Location**: `build/app/outputs/bundle/release/app-release.aab` (44.4MB)
- **Build Command**: `flutter build appbundle --no-tree-shake-icons`

## 📦 Required Subscription Products

Create these **exact** product IDs in Google Play Console → Monetize → Products → Subscriptions:

### Product IDs & Pricing (Must Match Exactly)
| Product ID | Name | Billing Period | Base Price (EGP) | USD Equivalent |
|------------|------|----------------|------------------|----------------|
| `premium_monthly` | Premium Monthly Plan | 1 Month | 39.99 EGP | ~$1.30 |
| `premium_yearly` | Premium Yearly Plan | 1 Year | 339.99 EGP | ~$11.00 |

> **Note**: Google Play will automatically convert these prices to the user's local currency based on their location. The USD equivalents are approximate and will vary with exchange rates.

## 🌍 Multi-Language Support (English + Arabic)

### English Product Names & Descriptions
- **Premium Monthly**: "Premium Monthly Plan"
  - *Description*: "Unlock premium features including unlimited vehicles, voice commands, and ad-free experience"
- **Premium Yearly**: "Premium Yearly Plan"
  - *Description*: "Annual premium subscription with voice commands, unlimited vehicles, and priority support. Save 30% compared to monthly billing!"

### Arabic Product Names & Descriptions (العربية)
- **Premium Monthly**: "الخطة المميزة الشهرية"
  - *Description*: "اكتشف الميزات المميزة بما في ذلك المركبات غير المحدودة والأوامر الصوتية والتجربة الخالية من الإعلانات"
- **Premium Yearly**: "الخطة المميزة السنوية"
  - *Description*: "اشتراك مميز سنوي مع الأوامر الصوتية والمركبات غير المحدودة والدعم الأولوي. وفر 30% مقارنة بالخطة الشهرية!"

## 🚀 Quick Setup Steps

1. **Upload Bundle** → Google Play Console → Release → Production (or Testing track)
   - Upload the `app-release.aab` file from `build/app/outputs/bundle/release/`
   - Add release notes in both English and Arabic

2. **Create Subscription Products**
   - Go to **Monetize** → **Products** → **Subscriptions**
   - Create both subscription products with exact IDs listed above
   - Set base prices in EGP as shown in the pricing table
   - Google Play will automatically handle currency conversion for users in different countries

3. **Add Translations**
   - For each product, click **Store listing** → **Manage translations**
   - Add Arabic (العربية) as a target language
   - Enter the Arabic names and descriptions from the table above

4. **Configure Subscription Details**
   - Set up free trial periods (7 days recommended)
   - Configure grace periods (3 days recommended)
   - Set up account hold (optional)

5. **Test Purchases**
   - Go to **Setup** → **License Testing**
   - Add test Gmail accounts under **License testers**
   - Set **Test response** to "RESPOND_NORMALLY"
   - Create an internal testing track with your app bundle
   - Test the purchase flow for each subscription tier

6. **Launch**
   - Complete all required sections (content rating, target audience, etc.)
   - Submit for review

## 💰 How Currency Conversion Works in the App

The subscription screen in the app is designed to automatically display prices in the user's local currency:

1. **Dynamic Price Display**:
   - The app uses Google Play's `ProductDetails` API to fetch localized prices
   - Prices are displayed exactly as provided by Google Play, including correct currency symbols
   - No hardcoded currency values are used in the UI

2. **Implementation Details**:
   - When the app loads, it queries Google Play for subscription products
   - Google Play returns prices already converted to the user's local currency
   - The app displays these localized prices directly to the user
   - If products can't be loaded, fallback prices in EGP are shown

3. **Testing Different Currencies**:
   - Use test accounts from different regions to verify currency conversion
   - Test purchases will show the appropriate currency based on the test account's region

## 🔧 Subscription Features

### Premium Tier Features
- Voice input for oil change logging
- Advanced analytics and insights
- Cloud backup and sync
- Priority customer support
- Multiple vehicle management
- Enhanced reporting

## ⚠️ Critical Notes
- Product IDs must match **exactly** as shown above
- The app will fail to load subscriptions if product IDs don't match
- Google Play automatically handles currency conversion based on user location
- Always test purchases with license testing accounts before release
- Set up both monthly and yearly billing cycles
- Configure grace periods (3 days recommended)

## 🆘 Troubleshooting Common Issues
- **"Product not found" errors**: Ensure subscription IDs match exactly in the app code
- **Purchase verification failures**: Check Google Play Console for proper product setup
- **Test purchases not working**: Verify test accounts are added to license testing
- **Arabic text not displaying**: Ensure Arabic translations are added for all products

## 📊 Testing Checklist
- [ ] App bundle uploaded successfully
- [ ] Both products created with correct IDs
- [ ] Base prices set correctly in EGP
- [ ] Arabic translations added for all products
- [ ] Test accounts added to License Testing
- [ ] Purchase flow tested on device
- [ ] Subscription verification working
- [ ] Grace periods configured
- [ ] Arabic UI tested on devices with Arabic language settings

---

**Remember**: Google Play handles currency conversion automatically. You only need to set the base price in EGP, and users worldwide will see prices in their local currency. 