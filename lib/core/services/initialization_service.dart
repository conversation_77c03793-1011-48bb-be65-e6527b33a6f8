import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/logger.dart';
import 'app_check_service.dart';
import 'analytics_service.dart';
import 'connectivity_service.dart';
import '../../services/notification_service.dart';
import '../../features/ads/application/app_open_ad_manager.dart';
import 'package:oil_change_tracker/features/ads/application/interstitial_ad_service.dart';
import 'package:oil_change_tracker/features/subscription/services/subscription_service.dart';

/// Provider for the AppOpenAdManager
final appOpenAdManagerProvider = Provider<AppOpenAdManager>((ref) {
  // Get the subscription service
  final subscriptionService = ref.watch(subscriptionServiceProvider);

  // Create the manager with the subscription service
  final manager = AppOpenAdManager(subscriptionService);

  ref.onDispose(() {
    manager.dispose();
  });
  return manager;
});

/// Service responsible for deferring heavy initialization tasks
/// to improve app startup time
class InitializationService {
  final ProviderContainer _container;
  bool _hasStartedBackgroundInit = false;
  bool _isBackgroundInitComplete = false;
  final Completer<void> _backgroundInitCompleter = Completer<void>();

  // Track critical initialization steps
  bool _appCheckActivated = false;
  final AppCheckService _appCheckService;
  final AppOpenAdManager _appOpenAdManager;

  InitializationService(
      this._container, this._appCheckService, this._appOpenAdManager);

  /// Whether background initialization has completed
  bool get isBackgroundInitComplete => _isBackgroundInitComplete;

  /// Future that completes when background initialization is done
  Future<void> get backgroundInitComplete => _backgroundInitCompleter.future;

  /// Initialize critical components needed for the app to function
  /// This should be kept as minimal as possible
  Future<void> initializeCriticalServices() async {
    if (_appCheckActivated) {
      AppLogger.info(
          'InitializationService: Critical services already initialized');
      return;
    }

    try {
      AppLogger.info('InitializationService: Starting critical initialization');

      // Initialize App Check - required for Firebase operations
      await _appCheckService.initialize();

      // Mark as initialized
      _appCheckActivated = true;
      AppLogger.info(
          'InitializationService: Critical initialization completed');
    } catch (e, stack) {
      AppLogger.error('InitializationService: Critical init error', e, stack);
      // Still mark as initialized to prevent blocking app startup
      _appCheckActivated = true;
    }
  }

  /// Start background initialization process
  /// This should be called after the UI is shown
  void startBackgroundInitialization() {
    if (_hasStartedBackgroundInit) return;
    _hasStartedBackgroundInit = true;

    AppLogger.info('InitializationService: Starting background initialization');

    // Use a microtask to ensure this doesn't block the main thread
    scheduleMicrotask(() async {
      try {
        // First do a short delay to let the UI settle
        await Future.delayed(const Duration(milliseconds: 100));

        // Initialize services in priority order
        await _initializeServicesInPriorityOrder();

        _isBackgroundInitComplete = true;
        _backgroundInitCompleter.complete();
        AppLogger.info(
            'InitializationService: Background initialization complete');
      } catch (e) {
        AppLogger.error(
            'InitializationService: Error during background initialization', e);
        // Complete anyway to prevent waiting forever
        if (!_backgroundInitCompleter.isCompleted) {
          _backgroundInitCompleter.complete();
        }
      }
    });
  }

  /// Initialize non-critical services in priority order
  Future<void> _initializeServicesInPriorityOrder() async {
    // PHASE 1: High priority services (user-facing)
    // First, initialize App Check fully - needed for many Firebase operations
    try {
      AppLogger.info('InitializationService: Initializing App Check service');
      await _appCheckService.initialize();
    } catch (e) {
      AppLogger.error(
          'InitializationService: App Check initialization error', e);
    }

    // PHASE 2: Medium priority services
    try {
      AppLogger.info(
          'InitializationService: Initializing connectivity service');
      final connectivityService = _container.read(connectivityServiceProvider);
      await connectivityService.initialize();
    } catch (e) {
      AppLogger.error(
          'InitializationService: Connectivity initialization error', e);
    }

    try {
      AppLogger.info('InitializationService: Initialize notification service');
      final notificationService = _container.read(notificationServiceProvider);
      await notificationService.initialize();
    } catch (e) {
      AppLogger.error(
          'InitializationService: Notification initialization error', e);
    }

    // PHASE 3: Low priority services (background)
    // Schedule with a delay to prioritize UI responsiveness
    Future.delayed(const Duration(milliseconds: 800), () {
      _initializeLowPriorityServices();
    });
  }

  /// Initialize low priority services that shouldn't block UI
  void _initializeLowPriorityServices() {
    // Initialize analytics in a non-blocking way
    scheduleMicrotask(() async {
      try {
        AppLogger.info('InitializationService: Initializing analytics service');
        await _initializeIfNotAccessed(analyticsServiceProvider);
      } catch (e) {
        AppLogger.error(
            'InitializationService: Analytics initialization error', e);
      }
    });

    // Initialize ad SDK with a significant delay to avoid impacting login
    Future.delayed(const Duration(seconds: 3), () {
      _initializeGoogleMobileAds();
    });
  }

  /// Initialize if not already accessed
  Future<void> _initializeIfNotAccessed<T>(
      ProviderListenable<T> provider) async {
    try {
      // Check if provider has already been initialized by someone else
      final providerState = _container
          .getAllProviderElements()
          .where((element) => element.provider == provider)
          .toList();

      // If already initialized, skip
      if (providerState.isNotEmpty) {
        AppLogger.info(
            'InitializationService: Provider ${provider.toString()} already initialized');
        return;
      }

      // Otherwise initialize - just read the provider
      // This will trigger initialization for any provider type
      _container.read(provider);

      // If it's an async provider, it might need special handling
      if (provider.toString().contains('Future') ||
          provider.toString().contains('Async')) {
        // Wait a bit to let async initialization proceed
        await Future.delayed(const Duration(milliseconds: 50));
      }
    } catch (e) {
      AppLogger.error('InitializationService: Error initializing provider', e);
    }
  }

  /// Initialize Google Mobile Ads with proper configuration
  Future<void> _initializeGoogleMobileAds() async {
    // Load ad configuration and services
    try {
      // First try to initialize app open ad manager - just use the injected instance directly
      AppLogger.info('InitializationService: Setting up App Open Ad Manager');
      // The _appOpenAdManager is already injected in the constructor, use it directly
      // Initialize but don't preload ads yet - this will set up the observers and SDK
      // but won't load actual ads until showAdsAfterLogin is set to true
      _appOpenAdManager.initialize();

      // Give some time for the initial setup to complete
      await Future.delayed(const Duration(milliseconds: 300));

      // Also initialize interstitial ads service but don't preload ads
      // The service itself will respect the login state
      AppLogger.info(
          'InitializationService: Setting up interstitial ad service');
      try {
        // Get the service but don't explicitly preload ads
        // The service will handle this after login
        final interstitialAdService =
            _container.read(interstitialAdServiceProvider.notifier);
        // Don't call preloadAd() here, let the login process trigger it
      } catch (e) {
        AppLogger.error(
            'InitializationService: Error initializing interstitial ads', e);
      }

      AppLogger.info(
          'InitializationService: Mobile ads initialization complete');
    } catch (e) {
      AppLogger.error(
          'InitializationService: Error initializing Google Mobile Ads', e);
    }
  }
}

final initializationServiceProvider = Provider<InitializationService>((ref) {
  final appCheckService = ref.watch(appCheckServiceProvider);
  final appOpenAdManager = ref.watch(appOpenAdManagerProvider);
  return InitializationService(
      ref.container, appCheckService, appOpenAdManager);
});
