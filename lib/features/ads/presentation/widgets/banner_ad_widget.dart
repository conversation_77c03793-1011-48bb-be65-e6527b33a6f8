import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:oil_change_tracker/core/utils/logger.dart';
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oil_change_tracker/features/subscription/providers/subscription_provider.dart';

class BannerAdWidget extends ConsumerStatefulWidget {
  // We'll keep the AdSize enum constant names to avoid breaking existing code
  static const banner = 'banner';
  static const mediumRectangle = 'mediumRectangle';

  final String adSize;

  const BannerAdWidget({super.key, this.adSize = BannerAdWidget.banner});

  @override
  ConsumerState<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends ConsumerState<BannerAdWidget>
    with WidgetsBindingObserver {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _isLoading = false;
  AdSize? _currentAdSize;
  int _loadAttempts = 0;
  static const int _maxLoadAttempts = 3;

  // For handling timeouts and retries
  Timer? _loadTimer;
  Timer? _refreshTimer;
  Timer? _retryTimer;
  static const int _adRefreshInterval = 60; // Refresh ad every 60 seconds
  static const int _loadTimeout =
      20; // Seconds to wait before considering load timed out

  // Replace with your real Ad Unit ID for production
  static const String _adUnitId = kDebugMode
      ? 'ca-app-pub-3940256099942544/6300978111' // Test ID
      : 'ca-app-pub-2630068952962472/8586168072'; // Production ID

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // No need to load here, didChangeDependencies will handle initial load
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load the ad when dependencies change (like MediaQuery)
    _loadAd();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // When app is resumed, check if ad needs refresh
      if (_bannerAd == null || !_isAdLoaded) {
        AppLogger.info('BannerAdWidget: App resumed, reloading ad');
        _loadAd();
      }
    }
  }

  /// Start periodic refresh timer
  void _startRefreshTimer() {
    // Cancel any existing timer
    _refreshTimer?.cancel();

    // Set up new timer for periodic refresh
    _refreshTimer =
        Timer.periodic(const Duration(seconds: _adRefreshInterval), (timer) {
      AppLogger.info('BannerAdWidget: Refresh timer triggered, reloading ad');
      if (mounted) {
        _loadAd(forceReload: true);
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _loadAd({bool forceReload = false}) async {
    // Check for premium subscription first and skip loading ads completely
    final hasSubscription =
        ref.read(subscriptionProvider).hasActiveSubscription;
    if (hasSubscription) {
      AppLogger.info(
          'BannerAdWidget: Skipping ad load - user has premium subscription');
      // Dispose any existing ad if user has premium
      if (_bannerAd != null) {
        await _bannerAd!.dispose();
        _bannerAd = null;
        if (mounted) setState(() => _isAdLoaded = false);
      }
      return;
    }

    // Don't reload if already in progress
    if (_isLoading) {
      AppLogger.info('BannerAdWidget: Ad load already in progress');
      return;
    }

    // Prevent reloading if ad is already loaded with correct size and not forced
    if (_isAdLoaded && !forceReload) {
      AppLogger.info('BannerAdWidget: Ad already loaded, skipping load');
      return;
    }

    // Check for too many attempts
    if (_loadAttempts >= _maxLoadAttempts) {
      AppLogger.warning(
          'BannerAdWidget: Too many load attempts, scheduling delayed retry');
      _scheduleRetry();
      return;
    }

    _isLoading = true;
    _loadAttempts++;

    // Set timeout to prevent indefinite loading state
    _loadTimer?.cancel();
    _loadTimer = Timer(const Duration(seconds: _loadTimeout), () {
      if (_isLoading) {
        AppLogger.warning(
            'BannerAdWidget: Ad load timed out after $_loadTimeout seconds');
        _isLoading = false;
        if (mounted) setState(() {});
        _scheduleRetry();
      }
    });

    // Get the appropriate ad size
    _currentAdSize = await _getAdSize();
    if (_currentAdSize == null) {
      AppLogger.warning(
          'BannerAdWidget: Invalid or unsupported ad size specified: ${widget.adSize}');
      _isLoading = false;
      if (mounted) setState(() => _isAdLoaded = false);
      return;
    }

    // If banner ad exists and size is the same, dispose it if force reload is requested
    if (_bannerAd != null) {
      if (forceReload || _bannerAd?.size != _currentAdSize) {
        AppLogger.info('BannerAdWidget: Disposing previous ad for reload');
        await _bannerAd!.dispose();
        _bannerAd = null;
        if (mounted) setState(() => _isAdLoaded = false);
      } else {
        AppLogger.info('BannerAdWidget: Ad already loaded with correct size.');
        _isLoading = false;
        return;
      }
    }

    AppLogger.info('BannerAdWidget: Loading new ad with size $_currentAdSize');
    _bannerAd = BannerAd(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      size: _currentAdSize!,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          AppLogger.info('BannerAdWidget: Ad loaded successfully.');
          _loadTimer?.cancel();
          _isLoading = false;
          _loadAttempts = 0; // Reset attempts counter on success

          if (!mounted) {
            ad.dispose(); // Dispose if widget is no longer mounted
            return;
          }

          setState(() {
            _bannerAd = ad as BannerAd; // Keep the loaded ad instance
            _isAdLoaded = true;
          });

          // Start refresh timer to reload ad periodically
          _startRefreshTimer();
        },
        onAdFailedToLoad: (ad, err) {
          AppLogger.error('BannerAdWidget: Failed to load ad: ${err.message}');
          _loadTimer?.cancel();
          _isLoading = false;
          ad.dispose();
          _bannerAd = null;

          if (mounted) {
            setState(() {
              _isAdLoaded = false;
            });
          }

          // Schedule retry
          _scheduleRetry();
        },
        onAdOpened: (Ad ad) => AppLogger.info('BannerAdWidget: Ad opened.'),
        onAdClosed: (Ad ad) {
          AppLogger.info('BannerAdWidget: Ad closed.');
          // Sometimes ads disappear after being closed
          // Reload after a short delay when this happens
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted && _bannerAd != null) {
              _loadAd(forceReload: true);
            }
          });
        },
        onAdImpression: (Ad ad) =>
            AppLogger.info('BannerAdWidget: Ad impression.'),
      ),
    )..load();
  }

  void _scheduleRetry() {
    _retryTimer?.cancel();

    // Calculate delay based on attempts
    final delay = _loadAttempts * 5; // 5, 10, 15 seconds

    AppLogger.info('BannerAdWidget: Scheduling reload in $delay seconds');
    _retryTimer = Timer(Duration(seconds: delay), () {
      if (mounted) {
        AppLogger.info('BannerAdWidget: Retrying ad load');
        _loadAd();
      }
    });
  }

  Future<AdSize?> _getAdSize() async {
    switch (widget.adSize) {
      case BannerAdWidget.banner:
        // Use adaptive banner size for better fit across devices
        final AnchoredAdaptiveBannerAdSize? size =
            await AdSize.getAnchoredAdaptiveBannerAdSize(
          MediaQuery.of(context).orientation,
          MediaQuery.of(context).size.width.truncate(),
        );
        return size;
      case BannerAdWidget.mediumRectangle:
        return AdSize.mediumRectangle;
      default:
        return null; // Invalid size
    }
  }

  @override
  void dispose() {
    AppLogger.info('BannerAdWidget: Disposing widget');
    _loadTimer?.cancel();
    _refreshTimer?.cancel();
    _retryTimer?.cancel();
    _bannerAd?.dispose();
    _bannerAd = null;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check for premium subscription
    final hasSubscription =
        ref.watch(subscriptionProvider).hasActiveSubscription;
    if (hasSubscription) {
      // Return empty container if user has premium subscription
      return const SizedBox.shrink();
    }

    final ad = _bannerAd;
    if (ad != null && _isAdLoaded) {
      return Container(
        width: ad.size.width.toDouble(),
        height: ad.size.height.toDouble(),
        alignment: Alignment.center,
        child: AdWidget(ad: ad),
      );
    } else {
      // Determine expected height based on widget configuration, not future ad size
      double expectedHeight = 50.0; // Default banner height
      if (widget.adSize == BannerAdWidget.mediumRectangle) {
        expectedHeight = 250.0;
      }
      // Use a SizedBox placeholder while loading or if failed
      return SizedBox(
        height: expectedHeight,
        width: double.infinity,
        // Optionally show a loading indicator or placeholder
        // child: Center(child: Text('Loading Ad...')),
      );
    }
  }
}
