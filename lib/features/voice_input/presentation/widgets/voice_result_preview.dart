import 'package:flutter/material.dart';
import '../../models/voice_command_result.dart';

/// A widget that displays a preview of the voice command result
class VoiceResultPreview extends StatelessWidget {
  /// The voice command result to display
  final VoiceCommandResult result;

  /// Creates a voice result preview widget
  const VoiceResultPreview({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCommandTypeHeader(context),
          const SizedBox(height: 8),
          _buildCommandDetails(context),
        ],
      ),
    );
  }

  Widget _buildCommandTypeHeader(BuildContext context) {
    IconData icon;
    String title;

    switch (result.commandType) {
      case VoiceCommandType.addOilChange:
        icon = Icons.oil_barrel;
        title = "Add Oil Change";
        break;
      case VoiceCommandType.addMaintenance:
        icon = Icons.build;
        title = "Add Maintenance";
        break;
      case VoiceCommandType.viewCarDetails:
        icon = Icons.directions_car;
        title = "View Car";
        break;
      case VoiceCommandType.unknown:
      default:
        icon = Icons.help_outline;
        title = "Unknown Command";
        break;
    }

    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildCommandDetails(BuildContext context) {
    final parameters = result.parameters;
    if (parameters.isEmpty) {
      return const Text("No parameters detected");
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: parameters.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Text(
                "${_formatParameterName(entry.key)}: ",
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
              Expanded(
                child: Text(
                  _formatParameterValue(entry.value),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatParameterName(String name) {
    return name
        .replaceAll(RegExp(r'([A-Z])'), ' \$1')
        .trim()
        .split(' ')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  String _formatParameterValue(dynamic value) {
    if (value == null) return "Not specified";
    if (value is DateTime) {
      return "${value.day}/${value.month}/${value.year}";
    }
    return value.toString();
  }
}
